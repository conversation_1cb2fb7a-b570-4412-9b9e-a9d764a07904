{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:48.540",
                    "level": "INFO",
                    "thread": "main",
                    "class": "com.genco.admin.GencoAdminApplication",
                    "message": "Starting GencoAdminApplication on LAPTOP-3I7P6FTL with PID 12076 (C:\Users\<USER>\Desktop\shop\api\genco-admin\target\classes started by 吴兴龙 in C:\Users\<USER>\Desktop\shop\api)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:48.542",
                    "level": "INFO",
                    "thread": "main",
                    "class": "com.genco.admin.GencoAdminApplication",
                    "message": "The following profiles are active: prod" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:50.108",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.d.r.c.RepositoryConfigurationDelegate",
                    "message": "Multiple Spring Data modules found, entering strict repository configuration mode!" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:50.110",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.d.r.c.RepositoryConfigurationDelegate",
                    "message": "Bootstrapping Spring Data Redis repositories in DEFAULT mode." }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:50.158",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.d.r.c.RepositoryConfigurationDelegate",
                    "message": "Finished Spring Data repository scanning in 32ms. Found 0 Redis repository interfaces." }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:51.055",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker",
                    "message": "Bean 'org.springframework.security.config.annotation.configuration.ObjectPostProcessorConfiguration' of type [org.springframework.security.config.annotation.configuration.ObjectPostProcessorConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:51.062",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker",
                    "message": "Bean 'objectPostProcessor' of type [org.springframework.security.config.annotation.configuration.AutowireBeanFactoryObjectPostProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:51.067",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker",
                    "message": "Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@22a4ca4a' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:51.068",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker",
                    "message": "Bean 'org.springframework.security.config.annotation.method.configuration.GlobalMethodSecurityConfiguration' of type [org.springframework.security.config.annotation.method.configuration.GlobalMethodSecurityConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:51.078",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker",
                    "message": "Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:51.374",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.b.w.embedded.tomcat.TomcatWebServer",
                    "message": "Tomcat initialized with port(s): 20000 (http)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:51.385",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.apache.coyote.http11.Http11NioProtocol",
                    "message": "Initializing ProtocolHandler ["http-nio-20000"]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:51.385",
                    "level": "INFO",
                    "thread": "main",
                    "class": "org.apache.catalina.core.StandardService",
                    "message": "Starting service [Tomcat]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:51.387",
                    "level": "INFO",
                    "thread": "main",
                    "class": "org.apache.catalina.core.StandardEngine",
                    "message": "Starting Servlet engine: [Apache Tomcat/9.0.33]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:51.679",
                    "level": "INFO",
                    "thread": "main",
                    "class": "org.apache.jasper.servlet.TldScanner",
                    "message": "At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time." }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:51.685",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.a.c.c.C.[Tomcat].[localhost].[/]",
                    "message": "Initializing Spring embedded WebApplicationContext" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:51.685",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.web.context.ContextLoader",
                    "message": "Root WebApplicationContext: initialization completed in 3099 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:56.525",
                    "level": "INFO",
                    "thread": "main",
                    "class": "c.g.s.s.impl.PaymentStrategyFactory",
                    "message": "支付策略初始化完成，支持的支付类型: [offline, alipay, weixin, xendit, yue, haipay]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:57.270",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.b.a.e.web.EndpointLinksResolver",
                    "message": "Exposing 2 endpoint(s) beneath base path '/actuator'" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:57.335",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.PropertySourcedRequestMappingHandlerMapping",
                    "message": "Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:57.358",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.s.concurrent.ThreadPoolTaskExecutor",
                    "message": "Initializing ExecutorService" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:57.359",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.s.concurrent.ThreadPoolTaskExecutor",
                    "message": "Initializing ExecutorService 'taskExecutor'" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:57.505",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.s.web.DefaultSecurityFilterChain",
                    "message": "Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2aac87ab, org.springframework.security.web.context.SecurityContextPersistenceFilter@870a9f2, org.springframework.security.web.header.HeaderWriterFilter@2a349a73, org.springframework.web.filter.CorsFilter@5d5574c7, org.springframework.web.filter.CorsFilter@5d5574c7, org.springframework.web.filter.CorsFilter@5d5574c7, org.springframework.security.web.authentication.logout.LogoutFilter@8b1bfdf, com.genco.admin.filter.JwtAuthenticationTokenFilter@7aa15a80, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7f2542f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1fc4483f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@c14bbab, org.springframework.security.web.session.SessionManagementFilter@3f6a9ba0, org.springframework.security.web.access.ExceptionTranslationFilter@4bac0be5, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3e4636c3]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.314",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.p.DocumentationPluginsBootstrapper",
                    "message": "Context refreshed" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.327",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.p.DocumentationPluginsBootstrapper",
                    "message": "Found 2 custom documentation plugin(s)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.365",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.s.ApiListingReferenceScanner",
                    "message": "Scanning for api listing references" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.646",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_1" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.655",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getByIdsUsingGET_1" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.662",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_1" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.673",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_1" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.683",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_1" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.687",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_1" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.712",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_2" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.715",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_2" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.728",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_2" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.730",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_3" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.744",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_3" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.769",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updatePhoneUsingPOST_1" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.778",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_4" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.809",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_2" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.815",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_5" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.818",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_3" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.823",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_2" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.825",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_4" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.827",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_6" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.829",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_7" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.837",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_3" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.855",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_8" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.862",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_4" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.885",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_3" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.888",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_5" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.907",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_4" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.919",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_9" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.924",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_5" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.928",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_4" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.930",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_6" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.931",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingPOST_1" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.951",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_10" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.957",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_5" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.958",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingPOST_2" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.965",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_11" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.971",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_5" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.980",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_12" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:58.996",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_6" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.016",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_13" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.027",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_6" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.049",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_14" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.056",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_7" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.062",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_6" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.064",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_7" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.067",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_7" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.071",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_15" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.075",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_8" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.077",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_7" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.080",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_8" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.085",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_16" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.086",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_9" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.089",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_8" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.089",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_8" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.093",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_17" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.095",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_9" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.105",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_18" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.106",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_10" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.110",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_9" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.111",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_9" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.112",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingPOST_3" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.114",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_10" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.119",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_19" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.120",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_11" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.123",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_10" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.124",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_10" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.125",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingPOST_4" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.126",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_11" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.130",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_20" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.132",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_12" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.133",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_11" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.134",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_11" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.137",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_12" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.141",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_21" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.142",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_13" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.145",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_12" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.147",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_12" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.153",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_22" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.156",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListTreeUsingGET_1" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.158",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_14" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.160",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_13" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.161",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingPOST_5" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.167",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_15" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.177",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_23" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.178",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_16" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.179",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_13" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.181",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_14" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.183",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_13" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.186",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_24" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.188",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_17" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.190",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_14" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.191",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_15" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.193",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_14" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.199",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_25" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.201",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_18" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.202",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_15" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.204",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_16" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.206",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingPOST_1" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.210",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_26" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.211",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_19" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.211",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_17" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.215",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_27" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.217",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_20" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.219",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_18" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.221",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_15" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.224",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_28" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.227",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_21" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.228",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_16" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.229",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_19" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.229",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingGET_1" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.231",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_16" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.235",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_29" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.236",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_22" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.238",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_17" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.238",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_20" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.239",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingGET_2" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.240",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_17" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.242",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_30" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.244",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_23" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.244",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_18" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.245",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_21" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.245",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingGET_3" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.247",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingPOST_2" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.249",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_31" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.250",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_19" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.250",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_22" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.285",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_32" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.289",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_24" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.294",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_23" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.302",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_33" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.306",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_24" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.307",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingPOST_6" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.308",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_18" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.309",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_34" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.309",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_25" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.310",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_20" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.311",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_25" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.314",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingPOST_1" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.317",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_35" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.318",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: balanceUsingPOST_1" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.321",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_36" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.323",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_19" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.324",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_37" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.324",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_26" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.325",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_21" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.325",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_26" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.339",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.s.ApiListingReferenceScanner",
                    "message": "Scanning for api listing references" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.353",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.s.concurrent.ThreadPoolTaskScheduler",
                    "message": "Initializing ExecutorService" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.358",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-2",
                    "class": "c.g.a.task.tiktok.TiktokTokenRefreshTask",
                    "message": "---TiktokTokenRefreshTask task------produce Data with fixed rate task: Execution Time - Fri Aug 08 09:19:59 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.359",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.apache.coyote.http11.Http11NioProtocol",
                    "message": "Starting ProtocolHandler ["http-nio-20000"]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.394",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.b.w.embedded.tomcat.TomcatWebServer",
                    "message": "Tomcat started on port(s): 20000 (http) with context path ''" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.397",
                    "level": "INFO",
                    "thread": "main",
                    "class": "com.genco.admin.GencoAdminApplication",
                    "message": "Started GencoAdminApplication in 11.55 seconds (JVM running for 12.197)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.452",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-3",
                    "class": "com.alibaba.druid.pool.DruidDataSource",
                    "message": "{dataSource-1} inited" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.811",
                    "level": "INFO",
                    "thread": "RMI TCP Connection(2)-************",
                    "class": "o.a.c.c.C.[Tomcat].[localhost].[/]",
                    "message": "Initializing Spring DispatcherServlet 'dispatcherServlet'" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.811",
                    "level": "INFO",
                    "thread": "RMI TCP Connection(2)-************",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Initializing Servlet 'dispatcherServlet'" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:59.818",
                    "level": "INFO",
                    "thread": "RMI TCP Connection(2)-************",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed initialization in 7 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:20:00.342",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-2",
                    "class": "c.g.a.task.tiktok.TiktokTokenRefreshTask",
                    "message": "Tiktok access_token 未到刷新时间，当前时间: 1754615999，过期时间: 1781838424" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:20:00.865",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-3",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始同步TikTok订单 - pageToken: null, startTime: Tue Aug 05 09:19:22 CST 2025, endTime: Fri Aug 08 09:19:22 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:20:02.083",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-3",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始调用TikTok API获取订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:20:26.847",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-3",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: f9151c5760c440fe830d26b11c29e60e, 类名: ValidateCodeController, 方法名: get, 用户ID: null, URI: /api/admin/validate/code/get, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:20:26.847",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: f2064095cccd4f94a0de4460071414b7, 类名: AdminLoginController, 方法名: getLoginPic, 用户ID: null, URI: /api/admin/getLoginPic, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:20:26.901",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-3",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"f9151c5760c440fe830d26b11c29e60e","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"get","requestParams":{"request":{"temp":"**********"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/validate/code/get","url":"http://127.0.0.1:20000/api/admin/validate/code/get"},"className":"com.genco.admin.controller.ValidateCodeController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","uri":"/api/admin/validate/code/get"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:20:26.901",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"f2064095cccd4f94a0de4460071414b7","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getLoginPic","requestParams":{"request":{"temp":"**********"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getLoginPic","url":"http://127.0.0.1:20000/api/admin/getLoginPic"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","uri":"/api/admin/getLoginPic"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:20:27.176",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-3",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.ValidateCodeController.get()，prams：[]，cost time：253546100 ns，cost：253 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:20:27.192",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-3",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: f9151c5760c440fe830d26b11c29e60e, 类名: ValidateCodeController, 方法名: get, 用户ID: null, 是否成功: true, 响应时间: 345ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:20:27.193",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-3",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"f9151c5760c440fe830d26b11c29e60e","requestBody":"未记录","responseTime":345,"methodName":"get","className":"com.genco.admin.controller.ValidateCodeController","isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:20:28.149",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getLoginPic()，prams：[]，cost time：********** ns，cost：1230 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:20:28.151",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: f2064095cccd4f94a0de4460071414b7, 类名: AdminLoginController, 方法名: getLoginPic, 用户ID: null, 是否成功: true, 响应时间: 1304ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:20:28.151",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"f2064095cccd4f94a0de4460071414b7","requestBody":"未记录","responseTime":1304,"methodName":"getLoginPic","className":"com.genco.admin.controller.AdminLoginController","isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:01.677",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 9b3f25b777934fcc91fa15b4d5b641cf, 类名: AdminLoginController, 方法名: SystemAdminLogin, 用户ID: null, URI: /api/admin/login, 方法: POST" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:01.678",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"9b3f25b777934fcc91fa15b4d5b641cf","method":"POST","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"SystemAdminLogin","requestParams":{"request":{},"method":"POST","request_filter":{},"header":{"sec-fetch-mode":"cors","content-length":"107","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","content-type":"application/json","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/login","url":"http://127.0.0.1:20000/api/admin/login"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","uri":"/api/admin/login"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:03.813",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.SystemAdminLogin(SystemAdminLoginRequest,HttpServletRequest)，prams：[SystemAdminLoginRequest(account=admin, pwd=BzX36YjvNR8o, key=44b44fab8b8690c87cd467fb0cbedf1d, code=Q3kw), SecurityContextHolderAwareRequestWrapper[ org.springframework.security.web.header.HeaderWriterFilter$HeaderWriterRequest@3f2674ed]]，cost time：********** ns，cost：1991 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:03.817",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 9b3f25b777934fcc91fa15b4d5b641cf, 类名: AdminLoginController, 方法名: SystemAdminLogin, 用户ID: null, 是否成功: true, 响应时间: 2140ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:03.817",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"9b3f25b777934fcc91fa15b4d5b641cf","requestBody":"未记录","responseTime":2140,"methodName":"SystemAdminLogin","className":"com.genco.admin.controller.AdminLoginController","isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:03.866",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-10",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 16c4430ee98347139583e5392156a0fa, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, URI: /api/admin/getAdminInfoByToken, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:03.866",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 2057c7a6fee643789c1bdbf18ce0056c, 类名: SystemStoreStaffController, 方法名: getList, 用户ID: 1, URI: /api/admin/system/store/staff/list, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:03.867",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-10",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"16c4430ee98347139583e5392156a0fa","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getAdminInfo","requestParams":{"request":{"temp":"1754616063","token":"8f7cc8029d8542acb239c2e4583d3190"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"8f7cc8029d8542acb239c2e4583d3190","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getAdminInfoByToken","url":"http://127.0.0.1:20000/api/admin/getAdminInfoByToken"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getAdminInfoByToken"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:03.867",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"2057c7a6fee643789c1bdbf18ce0056c","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getList","requestParams":{"request":{"temp":"1754616063","limit":"9999","page":"1"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"8f7cc8029d8542acb239c2e4583d3190","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/store/staff/list","url":"http://127.0.0.1:20000/api/admin/system/store/staff/list"},"className":"com.genco.admin.controller.SystemStoreStaffController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/store/staff/list"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:03.886",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-10",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getAdminInfo()，prams：[]，cost time：13254200 ns，cost：13 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:03.891",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-10",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 16c4430ee98347139583e5392156a0fa, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, 是否成功: true, 响应时间: 25ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:03.892",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-10",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"16c4430ee98347139583e5392156a0fa","requestBody":"未记录","responseTime":25,"methodName":"getAdminInfo","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:03.905",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 940c1e2d52af4b6c8d5474a08cb52a82, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, URI: /api/admin/getMenus, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:03.905",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"940c1e2d52af4b6c8d5474a08cb52a82","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getMenus","requestParams":{"request":{"temp":"1754616063"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"8f7cc8029d8542acb239c2e4583d3190","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getMenus","url":"http://127.0.0.1:20000/api/admin/getMenus"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getMenus"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:04.026",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getMenus()，prams：[]，cost time：120158700 ns，cost：120 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:04.044",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 940c1e2d52af4b6c8d5474a08cb52a82, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, 是否成功: true, 响应时间: 139ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:04.044",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"940c1e2d52af4b6c8d5474a08cb52a82","requestBody":"未记录","responseTime":139,"methodName":"getMenus","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:04.121",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemStoreStaffController.getList(Integer,PageParamRequest)，prams：[0, PageParamRequest(page=1, limit=9999)]，cost time：237566800 ns，cost：237 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:04.126",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 2057c7a6fee643789c1bdbf18ce0056c, 类名: SystemStoreStaffController, 方法名: getList, 用户ID: 1, 是否成功: true, 响应时间: 259ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:04.126",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-19",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 724d1a130d88439a8164155353c23cb9, 类名: SystemConfigController, 方法名: info, 用户ID: 1, URI: /api/admin/system/config/info, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:04.126",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-17",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 0dfaaf8875b042e78dcc75fd25e0c87c, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:04.126",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"2057c7a6fee643789c1bdbf18ce0056c","requestBody":"未记录","responseTime":259,"methodName":"getList","className":"com.genco.admin.controller.SystemStoreStaffController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:04.126",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-19",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"724d1a130d88439a8164155353c23cb9","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"info","requestParams":{"request":{"formId":"100","temp":"1754616064"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"8f7cc8029d8542acb239c2e4583d3190","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/info","url":"http://127.0.0.1:20000/api/admin/system/config/info"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/info"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:04.126",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-17",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"0dfaaf8875b042e78dcc75fd25e0c87c","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754616064","key":"site_logo_square"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"8f7cc8029d8542acb239c2e4583d3190","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:04.128",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: a8830441a3c34ab2a4986e1107e145a6, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:04.129",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"a8830441a3c34ab2a4986e1107e145a6","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754616064","key":"site_logo_lefttop"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"8f7cc8029d8542acb239c2e4583d3190","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:04.258",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-5",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始同步TikTok订单 - pageToken: null, startTime: Tue Aug 05 09:20:22 CST 2025, endTime: Fri Aug 08 09:20:22 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:04.333",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_lefttop]，cost time：202306300 ns，cost：202 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:04.338",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: a8830441a3c34ab2a4986e1107e145a6, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 211ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:04.339",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"a8830441a3c34ab2a4986e1107e145a6","requestBody":"未记录","responseTime":211,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:04.354",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-17",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_square]，cost time：226026600 ns，cost：226 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:04.358",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-17",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 0dfaaf8875b042e78dcc75fd25e0c87c, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 231ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:04.358",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-17",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"0dfaaf8875b042e78dcc75fd25e0c87c","requestBody":"未记录","responseTime":231,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:04.412",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-19",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.info(Integer)，prams：[100]，cost time：285101100 ns，cost：285 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:04.415",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-19",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 724d1a130d88439a8164155353c23cb9, 类名: SystemConfigController, 方法名: info, 用户ID: 1, 是否成功: true, 响应时间: 290ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:04.415",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-19",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"724d1a130d88439a8164155353c23cb9","requestBody":"未记录","responseTime":290,"methodName":"info","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:05.029",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-5",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始调用TikTok API获取订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:22:06.818",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-3",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始同步TikTok订单 - pageToken: null, startTime: Tue Aug 05 09:21:22 CST 2025, endTime: Fri Aug 08 09:21:22 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:22:07.484",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-3",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始调用TikTok API获取订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:23:09.369",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-8",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始同步TikTok订单 - pageToken: null, startTime: Tue Aug 05 09:22:22 CST 2025, endTime: Fri Aug 08 09:22:22 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:23:10.038",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-8",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始调用TikTok API获取订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:24:21.925",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-10",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始同步TikTok订单 - pageToken: null, startTime: Tue Aug 05 09:23:22 CST 2025, endTime: Fri Aug 08 09:23:22 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:24:22.617",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-10",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始调用TikTok API获取订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:25:24.516",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-6",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Tue Aug 05 09:25:24 CST 2025 至 Fri Aug 08 09:25:24 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:26:14.091",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-27",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: ba51cdda68b94598871cb28197c1a1a5, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, URI: /api/admin/getAdminInfoByToken, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:26:14.092",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-27",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"ba51cdda68b94598871cb28197c1a1a5","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getAdminInfo","requestParams":{"request":{"temp":"1754616374","token":"8f7cc8029d8542acb239c2e4583d3190"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"8f7cc8029d8542acb239c2e4583d3190","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getAdminInfoByToken","url":"http://127.0.0.1:20000/api/admin/getAdminInfoByToken"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getAdminInfoByToken"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:26:14.093",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-27",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getAdminInfo()，prams：[]，cost time：139600 ns，cost：0 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:26:14.097",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-27",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: ba51cdda68b94598871cb28197c1a1a5, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, 是否成功: true, 响应时间: 5ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:26:14.097",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-27",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"ba51cdda68b94598871cb28197c1a1a5","requestBody":"未记录","responseTime":5,"methodName":"getAdminInfo","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:26:14.109",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 2702684eeaff4d379a3edd1a8ab1f610, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, URI: /api/admin/getMenus, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:26:14.109",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"2702684eeaff4d379a3edd1a8ab1f610","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getMenus","requestParams":{"request":{"temp":"1754616374"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"8f7cc8029d8542acb239c2e4583d3190","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getMenus","url":"http://127.0.0.1:20000/api/admin/getMenus"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getMenus"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:26:14.340",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getMenus()，prams：[]，cost time：229773500 ns，cost：229 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:26:14.342",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 2702684eeaff4d379a3edd1a8ab1f610, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, 是否成功: true, 响应时间: 233ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:26:14.344",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"2702684eeaff4d379a3edd1a8ab1f610","requestBody":"未记录","responseTime":233,"methodName":"getMenus","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:26:14.405",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 1777bfe699274309ada9ccdf5a159137, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:26:14.405",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: b1ac6273b8bc44eda45bfa6783a0f21d, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:26:14.405",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-4",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: de9e93ca28834822892223b2e370c315, 类名: SystemConfigController, 方法名: info, 用户ID: 1, URI: /api/admin/system/config/info, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:26:14.406",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-4",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"de9e93ca28834822892223b2e370c315","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"info","requestParams":{"request":{"formId":"100","temp":"1754616374"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"8f7cc8029d8542acb239c2e4583d3190","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/info","url":"http://127.0.0.1:20000/api/admin/system/config/info"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/info"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:26:14.406",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"1777bfe699274309ada9ccdf5a159137","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754616374","key":"site_logo_square"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"8f7cc8029d8542acb239c2e4583d3190","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:26:14.406",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"b1ac6273b8bc44eda45bfa6783a0f21d","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754616374","key":"site_logo_lefttop"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"8f7cc8029d8542acb239c2e4583d3190","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:26:14.570",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-4",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.info(Integer)，prams：[100]，cost time：162510101 ns，cost：162 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:26:14.570",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_square]，cost time：162506101 ns，cost：162 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:26:14.573",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-4",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: de9e93ca28834822892223b2e370c315, 类名: SystemConfigController, 方法名: info, 用户ID: 1, 是否成功: true, 响应时间: 168ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:26:14.573",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 1777bfe699274309ada9ccdf5a159137, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 168ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:26:14.573",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-4",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"de9e93ca28834822892223b2e370c315","requestBody":"未记录","responseTime":168,"methodName":"info","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:26:14.573",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"1777bfe699274309ada9ccdf5a159137","requestBody":"未记录","responseTime":168,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:26:14.631",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_lefttop]，cost time：222734601 ns，cost：222 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:26:14.634",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: b1ac6273b8bc44eda45bfa6783a0f21d, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 228ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:26:14.634",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"b1ac6273b8bc44eda45bfa6783a0f21d","requestBody":"未记录","responseTime":228,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:26:17.987",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: ffd00f909c074864bb5db1bc7bd89904, 类名: SystemRoleController, 方法名: getList, 用户ID: 1, URI: /api/admin/system/role/list, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:26:17.988",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"ffd00f909c074864bb5db1bc7bd89904","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getList","requestParams":{"request":{"temp":"1754616377","limit":"20","page":"1"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"8f7cc8029d8542acb239c2e4583d3190","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/role/list","url":"http://127.0.0.1:20000/api/admin/system/role/list"},"className":"com.genco.admin.controller.SystemRoleController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/role/list"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:26:18.467",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemRoleController.getList(SystemRoleSearchRequest,PageParamRequest)，prams：[SystemRoleSearchRequest(roleName=null, status=null), PageParamRequest(page=1, limit=20)]，cost time：460960700 ns，cost：460 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:26:18.473",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: ffd00f909c074864bb5db1bc7bd89904, 类名: SystemRoleController, 方法名: getList, 用户ID: 1, 是否成功: true, 响应时间: 486ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:26:18.474",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"ffd00f909c074864bb5db1bc7bd89904","requestBody":"未记录","responseTime":486,"methodName":"getList","className":"com.genco.admin.controller.SystemRoleController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:26:25.646",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-13",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Tue Aug 05 09:26:25 CST 2025 至 Fri Aug 08 09:26:25 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:27:26.801",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-4",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Tue Aug 05 09:27:26 CST 2025 至 Fri Aug 08 09:27:26 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:28:19.575",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 9be18385b66f4c1291c8222f2ca5c66f, 类名: SystemUserLevelController, 方法名: getList, 用户ID: 1, URI: /api/admin/system/user/level/list, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:28:19.575",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"9be18385b66f4c1291c8222f2ca5c66f","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getList","requestParams":{"request":{"temp":"1754616499"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"8f7cc8029d8542acb239c2e4583d3190","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/user/level/list","url":"http://127.0.0.1:20000/api/admin/system/user/level/list"},"className":"com.genco.admin.controller.SystemUserLevelController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/user/level/list"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:28:19.811",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemUserLevelController.getList()，prams：[]，cost time：234216300 ns，cost：234 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:28:19.818",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 9be18385b66f4c1291c8222f2ca5c66f, 类名: SystemUserLevelController, 方法名: getList, 用户ID: 1, 是否成功: true, 响应时间: 243ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:28:19.820",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"9be18385b66f4c1291c8222f2ca5c66f","requestBody":"未记录","responseTime":243,"methodName":"getList","className":"com.genco.admin.controller.SystemUserLevelController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:28:25.054",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 5b3fe20eb1fb422ea3e6d2f4ad4a40fc, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, URI: /api/admin/getAdminInfoByToken, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:28:25.055",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"5b3fe20eb1fb422ea3e6d2f4ad4a40fc","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getAdminInfo","requestParams":{"request":{"temp":"1754616505","token":"8f7cc8029d8542acb239c2e4583d3190"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"8f7cc8029d8542acb239c2e4583d3190","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getAdminInfoByToken","url":"http://127.0.0.1:20000/api/admin/getAdminInfoByToken"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getAdminInfoByToken"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:28:25.056",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getAdminInfo()，prams：[]，cost time：135000 ns，cost：0 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:28:25.058",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 5b3fe20eb1fb422ea3e6d2f4ad4a40fc, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, 是否成功: true, 响应时间: 3ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:28:25.058",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"5b3fe20eb1fb422ea3e6d2f4ad4a40fc","requestBody":"未记录","responseTime":3,"methodName":"getAdminInfo","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:28:25.118",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-17",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: d192e830186641c5b99802ad2434c31f, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, URI: /api/admin/getMenus, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:28:25.118",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-17",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"d192e830186641c5b99802ad2434c31f","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getMenus","requestParams":{"request":{"temp":"1754616505"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"8f7cc8029d8542acb239c2e4583d3190","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getMenus","url":"http://127.0.0.1:20000/api/admin/getMenus"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getMenus"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:28:25.348",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-17",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getMenus()，prams：[]，cost time：228488300 ns，cost：228 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:28:25.350",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-17",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: d192e830186641c5b99802ad2434c31f, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, 是否成功: true, 响应时间: 233ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:28:25.351",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-17",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"d192e830186641c5b99802ad2434c31f","requestBody":"未记录","responseTime":233,"methodName":"getMenus","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:28:25.422",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-23",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 126994b5bde54b30ad79fd2dd97b23b1, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:28:25.422",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-24",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 3b9e031df8ac4b149c0d57a943b366c5, 类名: SystemUserLevelController, 方法名: getList, 用户ID: 1, URI: /api/admin/system/user/level/list, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:28:25.422",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-22",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: b1343a35c43c41838593c6cfa5f6ee44, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:28:25.422",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-23",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"126994b5bde54b30ad79fd2dd97b23b1","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754616505","key":"site_logo_square"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"8f7cc8029d8542acb239c2e4583d3190","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:28:25.422",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-22",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"b1343a35c43c41838593c6cfa5f6ee44","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754616505","key":"site_logo_lefttop"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"8f7cc8029d8542acb239c2e4583d3190","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:28:25.422",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-24",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"3b9e031df8ac4b149c0d57a943b366c5","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getList","requestParams":{"request":{"temp":"1754616505"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"8f7cc8029d8542acb239c2e4583d3190","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/user/level/list","url":"http://127.0.0.1:20000/api/admin/system/user/level/list"},"className":"com.genco.admin.controller.SystemUserLevelController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/user/level/list"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:28:25.584",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-24",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemUserLevelController.getList()，prams：[]，cost time：160234400 ns，cost：160 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:28:25.588",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-23",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_square]，cost time：163408500 ns，cost：163 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:28:25.588",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-24",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 3b9e031df8ac4b149c0d57a943b366c5, 类名: SystemUserLevelController, 方法名: getList, 用户ID: 1, 是否成功: true, 响应时间: 164ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:28:25.588",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-24",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"3b9e031df8ac4b149c0d57a943b366c5","requestBody":"未记录","responseTime":164,"methodName":"getList","className":"com.genco.admin.controller.SystemUserLevelController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:28:25.590",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-23",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 126994b5bde54b30ad79fd2dd97b23b1, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 168ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:28:25.590",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-23",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"126994b5bde54b30ad79fd2dd97b23b1","requestBody":"未记录","responseTime":168,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:28:25.646",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-22",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_lefttop]，cost time：222264400 ns，cost：222 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:28:25.648",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-22",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: b1343a35c43c41838593c6cfa5f6ee44, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 226ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:28:25.648",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-22",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"b1343a35c43c41838593c6cfa5f6ee44","requestBody":"未记录","responseTime":226,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:28:27.207",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-16",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Tue Aug 05 09:28:27 CST 2025 至 Fri Aug 08 09:28:27 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:29:27.612",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-9",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Tue Aug 05 09:29:27 CST 2025 至 Fri Aug 08 09:29:27 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:30:00.355",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-5",
                    "class": "c.g.a.task.tiktok.TiktokTokenRefreshTask",
                    "message": "---TiktokTokenRefreshTask task------produce Data with fixed rate task: Execution Time - Fri Aug 08 09:30:00 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:30:00.439",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-5",
                    "class": "c.g.a.task.tiktok.TiktokTokenRefreshTask",
                    "message": "Tiktok access_token 未到刷新时间，当前时间: 1754616600，过期时间: 1781838424" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:30:28.099",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-10",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Tue Aug 05 09:30:27 CST 2025 至 Fri Aug 08 09:30:27 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:30:41.945",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-1",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 4173d35ceacb410c94b9aca0e85d8080, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, URI: /api/admin/getAdminInfoByToken, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:30:41.945",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-1",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"4173d35ceacb410c94b9aca0e85d8080","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getAdminInfo","requestParams":{"request":{"temp":"1754616641","token":"8f7cc8029d8542acb239c2e4583d3190"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"8f7cc8029d8542acb239c2e4583d3190","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getAdminInfoByToken","url":"http://127.0.0.1:20000/api/admin/getAdminInfoByToken"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getAdminInfoByToken"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:30:41.946",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-1",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getAdminInfo()，prams：[]，cost time：177300 ns，cost：0 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:30:41.948",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-1",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 4173d35ceacb410c94b9aca0e85d8080, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, 是否成功: true, 响应时间: 4ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:30:41.949",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-1",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"4173d35ceacb410c94b9aca0e85d8080","requestBody":"未记录","responseTime":4,"methodName":"getAdminInfo","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:30:41.963",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 2266a15230f442e49519c0e921d4f99e, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, URI: /api/admin/getMenus, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:30:41.963",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"2266a15230f442e49519c0e921d4f99e","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getMenus","requestParams":{"request":{"temp":"1754616641"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"8f7cc8029d8542acb239c2e4583d3190","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getMenus","url":"http://127.0.0.1:20000/api/admin/getMenus"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getMenus"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:30:42.052",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getMenus()，prams：[]，cost time：86934900 ns，cost：86 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:30:42.054",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 2266a15230f442e49519c0e921d4f99e, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, 是否成功: true, 响应时间: 91ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:30:42.055",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"2266a15230f442e49519c0e921d4f99e","requestBody":"未记录","responseTime":91,"methodName":"getMenus","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:30:42.125",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-8",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 255856fcb98246c78d71e3f382d9d026, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:30:42.125",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 170aefa1ab1a410680b89fc7d20b5115, 类名: SystemUserLevelController, 方法名: getList, 用户ID: 1, URI: /api/admin/system/user/level/list, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:30:42.125",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 773ca83f36204e3c9ed84330e2c1ce5d, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:30:42.126",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-8",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"255856fcb98246c78d71e3f382d9d026","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754616642","key":"site_logo_square"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"8f7cc8029d8542acb239c2e4583d3190","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:30:42.126",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"773ca83f36204e3c9ed84330e2c1ce5d","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754616642","key":"site_logo_lefttop"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"8f7cc8029d8542acb239c2e4583d3190","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:30:42.126",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"170aefa1ab1a410680b89fc7d20b5115","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getList","requestParams":{"request":{"temp":"1754616642"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"8f7cc8029d8542acb239c2e4583d3190","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/user/level/list","url":"http://127.0.0.1:20000/api/admin/system/user/level/list"},"className":"com.genco.admin.controller.SystemUserLevelController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/user/level/list"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:30:42.213",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemUserLevelController.getList()，prams：[]，cost time：86430800 ns，cost：86 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:30:42.216",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 170aefa1ab1a410680b89fc7d20b5115, 类名: SystemUserLevelController, 方法名: getList, 用户ID: 1, 是否成功: true, 响应时间: 91ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:30:42.216",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"170aefa1ab1a410680b89fc7d20b5115","requestBody":"未记录","responseTime":91,"methodName":"getList","className":"com.genco.admin.controller.SystemUserLevelController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:30:42.320",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-8",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_square]，cost time：193904700 ns，cost：193 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:30:42.322",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-8",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 255856fcb98246c78d71e3f382d9d026, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 197ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:30:42.322",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-8",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"255856fcb98246c78d71e3f382d9d026","requestBody":"未记录","responseTime":197,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:30:42.577",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_lefttop]，cost time：451523600 ns，cost：451 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:30:42.580",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 773ca83f36204e3c9ed84330e2c1ce5d, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 455ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:30:42.580",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"773ca83f36204e3c9ed84330e2c1ce5d","requestBody":"未记录","responseTime":455,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:31:29.245",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-21",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Tue Aug 05 09:31:28 CST 2025 至 Fri Aug 08 09:31:28 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:32:30.388",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-6",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Tue Aug 05 09:32:29 CST 2025 至 Fri Aug 08 09:32:29 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:06.153",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 87ad15b3a94e4dd3882f75c137a6aba2, 类名: ValidateCodeController, 方法名: get, 用户ID: null, URI: /api/admin/validate/code/get, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:06.153",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-16",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 6a4edc8cb0e8470fa5a0237a40f3200a, 类名: AdminLoginController, 方法名: getLoginPic, 用户ID: null, URI: /api/admin/getLoginPic, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:06.153",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"87ad15b3a94e4dd3882f75c137a6aba2","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"get","requestParams":{"request":{"temp":"**********"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/validate/code/get","url":"http://127.0.0.1:20000/api/admin/validate/code/get"},"className":"com.genco.admin.controller.ValidateCodeController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","uri":"/api/admin/validate/code/get"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:06.155",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-16",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"6a4edc8cb0e8470fa5a0237a40f3200a","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getLoginPic","requestParams":{"request":{"temp":"**********"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getLoginPic","url":"http://127.0.0.1:20000/api/admin/getLoginPic"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","uri":"/api/admin/getLoginPic"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:06.161",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.ValidateCodeController.get()，prams：[]，cost time：6711400 ns，cost：6 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:06.162",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 87ad15b3a94e4dd3882f75c137a6aba2, 类名: ValidateCodeController, 方法名: get, 用户ID: null, 是否成功: true, 响应时间: 9ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:06.162",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"87ad15b3a94e4dd3882f75c137a6aba2","requestBody":"未记录","responseTime":9,"methodName":"get","className":"com.genco.admin.controller.ValidateCodeController","isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:07.268",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-16",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getLoginPic()，prams：[]，cost time：********** ns，cost：1114 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:07.269",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-16",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 6a4edc8cb0e8470fa5a0237a40f3200a, 类名: AdminLoginController, 方法名: getLoginPic, 用户ID: null, 是否成功: true, 响应时间: 1116ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:07.270",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-16",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"6a4edc8cb0e8470fa5a0237a40f3200a","requestBody":"未记录","responseTime":1116,"methodName":"getLoginPic","className":"com.genco.admin.controller.AdminLoginController","isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:18.042",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-17",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 832d0e1b3ca843868ab8a5b37a929026, 类名: AdminLoginController, 方法名: SystemAdminLogin, 用户ID: null, URI: /api/admin/login, 方法: POST" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:18.043",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-17",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"832d0e1b3ca843868ab8a5b37a929026","method":"POST","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"SystemAdminLogin","requestParams":{"request":{},"method":"POST","request_filter":{},"header":{"sec-fetch-mode":"cors","content-length":"107","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","content-type":"application/json","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/login","url":"http://127.0.0.1:20000/api/admin/login"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","uri":"/api/admin/login"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:19.178",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-17",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.SystemAdminLogin(SystemAdminLoginRequest,HttpServletRequest)，prams：[SystemAdminLoginRequest(account=admin, pwd=BzX36YjvNR8o, key=f79ce42c6176a78d5345e9aa49967e39, code=5vra), SecurityContextHolderAwareRequestWrapper[ org.springframework.security.web.header.HeaderWriterFilter$HeaderWriterRequest@25426b36]]，cost time：********** ns，cost：1134 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:19.180",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-17",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 832d0e1b3ca843868ab8a5b37a929026, 类名: AdminLoginController, 方法名: SystemAdminLogin, 用户ID: null, 是否成功: true, 响应时间: 1138ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:19.180",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-17",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"832d0e1b3ca843868ab8a5b37a929026","requestBody":"未记录","responseTime":1138,"methodName":"SystemAdminLogin","className":"com.genco.admin.controller.AdminLoginController","isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:19.194",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-23",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: ec991f0a8fa04164bca3680900f6edc7, 类名: SystemStoreStaffController, 方法名: getList, 用户ID: 1, URI: /api/admin/system/store/staff/list, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:19.194",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 2f5bb4c41ea043448b58e94f355c5f81, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, URI: /api/admin/getAdminInfoByToken, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:19.194",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-23",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"ec991f0a8fa04164bca3680900f6edc7","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getList","requestParams":{"request":{"temp":"1754616799","limit":"9999","page":"1"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/store/staff/list","url":"http://127.0.0.1:20000/api/admin/system/store/staff/list"},"className":"com.genco.admin.controller.SystemStoreStaffController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/store/staff/list"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:19.194",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"2f5bb4c41ea043448b58e94f355c5f81","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getAdminInfo","requestParams":{"request":{"temp":"1754616799","token":"0703ac2ae5d44addba7e1a54f5720092"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getAdminInfoByToken","url":"http://127.0.0.1:20000/api/admin/getAdminInfoByToken"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getAdminInfoByToken"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:19.194",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getAdminInfo()，prams：[]，cost time：41000 ns，cost：0 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:19.195",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 2f5bb4c41ea043448b58e94f355c5f81, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, 是否成功: true, 响应时间: 2ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:19.196",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"2f5bb4c41ea043448b58e94f355c5f81","requestBody":"未记录","responseTime":2,"methodName":"getAdminInfo","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:19.207",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-24",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 1e8c7caeb22b4f18b64c49b13914d0d5, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, URI: /api/admin/getMenus, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:19.207",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-24",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"1e8c7caeb22b4f18b64c49b13914d0d5","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getMenus","requestParams":{"request":{"temp":"1754616799"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getMenus","url":"http://127.0.0.1:20000/api/admin/getMenus"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getMenus"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:19.370",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-24",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getMenus()，prams：[]，cost time：162424300 ns，cost：162 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:19.372",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-24",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 1e8c7caeb22b4f18b64c49b13914d0d5, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, 是否成功: true, 响应时间: 165ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:19.372",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-24",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"1e8c7caeb22b4f18b64c49b13914d0d5","requestBody":"未记录","responseTime":165,"methodName":"getMenus","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:19.419",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-23",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemStoreStaffController.getList(Integer,PageParamRequest)，prams：[0, PageParamRequest(page=1, limit=9999)]，cost time：224133200 ns，cost：224 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:19.421",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-23",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: ec991f0a8fa04164bca3680900f6edc7, 类名: SystemStoreStaffController, 方法名: getList, 用户ID: 1, 是否成功: true, 响应时间: 227ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:19.421",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-23",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"ec991f0a8fa04164bca3680900f6edc7","requestBody":"未记录","responseTime":227,"methodName":"getList","className":"com.genco.admin.controller.SystemStoreStaffController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:19.449",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 582d7db9020d4e3e8db9667595d312c4, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:19.449",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: fd50e91e53a242c5b27286ad90b7246c, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:19.450",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"582d7db9020d4e3e8db9667595d312c4","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754616799","key":"site_logo_lefttop"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:19.450",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-30",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 034ca55020f34ab394712b6d8d5b821f, 类名: SystemConfigController, 方法名: info, 用户ID: 1, URI: /api/admin/system/config/info, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:19.450",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"fd50e91e53a242c5b27286ad90b7246c","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754616799","key":"site_logo_square"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:19.450",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-30",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"034ca55020f34ab394712b6d8d5b821f","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"info","requestParams":{"request":{"formId":"100","temp":"1754616799"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/info","url":"http://127.0.0.1:20000/api/admin/system/config/info"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/info"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:19.527",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_lefttop]，cost time：77484400 ns，cost：77 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:19.530",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 582d7db9020d4e3e8db9667595d312c4, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 79ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:19.530",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"582d7db9020d4e3e8db9667595d312c4","requestBody":"未记录","responseTime":79,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:19.624",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_square]，cost time：173174800 ns，cost：173 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:19.626",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: fd50e91e53a242c5b27286ad90b7246c, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 176ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:19.626",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"fd50e91e53a242c5b27286ad90b7246c","requestBody":"未记录","responseTime":176,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:19.673",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-30",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.info(Integer)，prams：[100]，cost time：222619300 ns，cost：222 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:19.674",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-30",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 034ca55020f34ab394712b6d8d5b821f, 类名: SystemConfigController, 方法名: info, 用户ID: 1, 是否成功: true, 响应时间: 224ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:19.674",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-30",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"034ca55020f34ab394712b6d8d5b821f","requestBody":"未记录","responseTime":224,"methodName":"info","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:24.361",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-3",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 7fd9bce207dc462a85db135a302f674d, 类名: SystemUserLevelController, 方法名: getList, 用户ID: 1, URI: /api/admin/system/user/level/list, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:24.361",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-3",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"7fd9bce207dc462a85db135a302f674d","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getList","requestParams":{"request":{"temp":"1754616804"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/user/level/list","url":"http://127.0.0.1:20000/api/admin/system/user/level/list"},"className":"com.genco.admin.controller.SystemUserLevelController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/user/level/list"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:24.591",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-3",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemUserLevelController.getList()，prams：[]，cost time：229754600 ns，cost：229 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:24.595",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-3",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 7fd9bce207dc462a85db135a302f674d, 类名: SystemUserLevelController, 方法名: getList, 用户ID: 1, 是否成功: true, 响应时间: 234ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:24.595",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-3",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"7fd9bce207dc462a85db135a302f674d","requestBody":"未记录","responseTime":234,"methodName":"getList","className":"com.genco.admin.controller.SystemUserLevelController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:26.785",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-6",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 901d924a47064973b4d520774a526c2e, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, URI: /api/admin/getAdminInfoByToken, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:26.785",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-6",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"901d924a47064973b4d520774a526c2e","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getAdminInfo","requestParams":{"request":{"temp":"1754616806","token":"0703ac2ae5d44addba7e1a54f5720092"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getAdminInfoByToken","url":"http://127.0.0.1:20000/api/admin/getAdminInfoByToken"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getAdminInfoByToken"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:26.786",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-6",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getAdminInfo()，prams：[]，cost time：50300 ns，cost：0 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:26.788",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-6",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 901d924a47064973b4d520774a526c2e, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, 是否成功: true, 响应时间: 4ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:26.788",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-6",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"901d924a47064973b4d520774a526c2e","requestBody":"未记录","responseTime":4,"methodName":"getAdminInfo","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:26.799",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: f8428653c7234f2ebb7d2b463ec67508, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, URI: /api/admin/getMenus, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:26.799",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"f8428653c7234f2ebb7d2b463ec67508","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getMenus","requestParams":{"request":{"temp":"1754616806"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getMenus","url":"http://127.0.0.1:20000/api/admin/getMenus"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getMenus"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:27.022",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getMenus()，prams：[]，cost time：223335400 ns，cost：223 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:27.025",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: f8428653c7234f2ebb7d2b463ec67508, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, 是否成功: true, 响应时间: 227ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:27.025",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"f8428653c7234f2ebb7d2b463ec67508","requestBody":"未记录","responseTime":227,"methodName":"getMenus","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:27.082",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-14",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 9ac45e7c5b574fcfbd06159c0ce6e662, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:27.082",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-10",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 982bb24188284137916b707398a05a54, 类名: SystemUserLevelController, 方法名: getList, 用户ID: 1, URI: /api/admin/system/user/level/list, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:27.082",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-15",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: f46697c40a4d4ec390e6e9d3c87f3bc1, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:27.082",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-14",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"9ac45e7c5b574fcfbd06159c0ce6e662","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754616807","key":"site_logo_lefttop"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:27.082",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-15",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"f46697c40a4d4ec390e6e9d3c87f3bc1","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754616807","key":"site_logo_square"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:27.082",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-10",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"982bb24188284137916b707398a05a54","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getList","requestParams":{"request":{"temp":"1754616807"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/user/level/list","url":"http://127.0.0.1:20000/api/admin/system/user/level/list"},"className":"com.genco.admin.controller.SystemUserLevelController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/user/level/list"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:27.160",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-15",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_square]，cost time：77276100 ns，cost：77 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:27.161",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-10",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemUserLevelController.getList()，prams：[]，cost time：78096400 ns，cost：78 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:27.162",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-15",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: f46697c40a4d4ec390e6e9d3c87f3bc1, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 80ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:27.162",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-15",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"f46697c40a4d4ec390e6e9d3c87f3bc1","requestBody":"未记录","responseTime":80,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:27.162",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-10",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 982bb24188284137916b707398a05a54, 类名: SystemUserLevelController, 方法名: getList, 用户ID: 1, 是否成功: true, 响应时间: 81ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:27.162",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-10",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"982bb24188284137916b707398a05a54","requestBody":"未记录","responseTime":81,"methodName":"getList","className":"com.genco.admin.controller.SystemUserLevelController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:27.305",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-14",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_lefttop]，cost time：221750300 ns，cost：221 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:27.311",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-14",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 9ac45e7c5b574fcfbd06159c0ce6e662, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 225ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:27.311",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-14",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"9ac45e7c5b574fcfbd06159c0ce6e662","requestBody":"未记录","responseTime":225,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:30.810",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-24",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Tue Aug 05 09:33:30 CST 2025 至 Fri Aug 08 09:33:30 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:34:31.238",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-13",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Tue Aug 05 09:34:31 CST 2025 至 Fri Aug 08 09:34:31 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:35:31.660",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-27",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Tue Aug 05 09:35:31 CST 2025 至 Fri Aug 08 09:35:31 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:36:32.081",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-4",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Tue Aug 05 09:36:31 CST 2025 至 Fri Aug 08 09:36:31 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:37:32.503",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-30",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Tue Aug 05 09:37:32 CST 2025 至 Fri Aug 08 09:37:32 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:38:33.089",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-8",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Tue Aug 05 09:38:32 CST 2025 至 Fri Aug 08 09:38:32 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:39:33.506",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-18",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Tue Aug 05 09:39:33 CST 2025 至 Fri Aug 08 09:39:33 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:40:00.451",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-19",
                    "class": "c.g.a.task.tiktok.TiktokTokenRefreshTask",
                    "message": "---TiktokTokenRefreshTask task------produce Data with fixed rate task: Execution Time - Fri Aug 08 09:40:00 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:40:00.532",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-19",
                    "class": "c.g.a.task.tiktok.TiktokTokenRefreshTask",
                    "message": "Tiktok access_token 未到刷新时间，当前时间: 1754617200，过期时间: 1781838424" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:40:33.909",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-20",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Tue Aug 05 09:40:33 CST 2025 至 Fri Aug 08 09:40:33 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:41:34.334",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-11",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Tue Aug 05 09:41:34 CST 2025 至 Fri Aug 08 09:41:34 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:42:34.754",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-23",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Tue Aug 05 09:42:34 CST 2025 至 Fri Aug 08 09:42:34 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:43:31.706",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: dc1b82e542d74152b248ea5b276c7fa8, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, URI: /api/admin/getAdminInfoByToken, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:43:31.707",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"dc1b82e542d74152b248ea5b276c7fa8","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getAdminInfo","requestParams":{"request":{"temp":"1754617411","token":"0703ac2ae5d44addba7e1a54f5720092"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getAdminInfoByToken","url":"http://127.0.0.1:20000/api/admin/getAdminInfoByToken"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getAdminInfoByToken"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:43:31.707",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getAdminInfo()，prams：[]，cost time：64500 ns，cost：0 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:43:31.710",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: dc1b82e542d74152b248ea5b276c7fa8, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, 是否成功: true, 响应时间: 3ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:43:31.710",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"dc1b82e542d74152b248ea5b276c7fa8","requestBody":"未记录","responseTime":3,"methodName":"getAdminInfo","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:43:31.738",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-24",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: c6d52d12237241cb8a0aa1da39a474b2, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, URI: /api/admin/getMenus, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:43:31.740",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-24",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"c6d52d12237241cb8a0aa1da39a474b2","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getMenus","requestParams":{"request":{"temp":"1754617411"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getMenus","url":"http://127.0.0.1:20000/api/admin/getMenus"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getMenus"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:43:31.852",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-24",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getMenus()，prams：[]，cost time：112656100 ns，cost：112 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:43:31.854",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-24",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: c6d52d12237241cb8a0aa1da39a474b2, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, 是否成功: true, 响应时间: 115ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:43:31.854",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-24",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"c6d52d12237241cb8a0aa1da39a474b2","requestBody":"未记录","responseTime":115,"methodName":"getMenus","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:43:32.081",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-25",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: ec3387ce9a46495695d183f60d81414e, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:43:32.081",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: cb3ce49fe3454ae49c7fa480a03825e0, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:43:32.082",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-25",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"ec3387ce9a46495695d183f60d81414e","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754617412","key":"site_logo_lefttop"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:43:32.082",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"cb3ce49fe3454ae49c7fa480a03825e0","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754617412","key":"site_logo_square"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:43:32.084",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: d507a6b484324aedab5452c21dcbcdb9, 类名: SystemUserLevelController, 方法名: getList, 用户ID: 1, URI: /api/admin/system/user/level/list, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:43:32.084",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"d507a6b484324aedab5452c21dcbcdb9","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getList","requestParams":{"request":{"temp":"1754617412"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/user/level/list","url":"http://127.0.0.1:20000/api/admin/system/user/level/list"},"className":"com.genco.admin.controller.SystemUserLevelController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/user/level/list"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:43:32.159",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-25",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_lefttop]，cost time：76852500 ns，cost：76 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:43:32.161",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-25",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: ec3387ce9a46495695d183f60d81414e, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 80ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:43:32.161",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-25",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"ec3387ce9a46495695d183f60d81414e","requestBody":"未记录","responseTime":80,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:43:32.237",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemUserLevelController.getList()，prams：[]，cost time：152744800 ns，cost：152 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:43:32.240",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: d507a6b484324aedab5452c21dcbcdb9, 类名: SystemUserLevelController, 方法名: getList, 用户ID: 1, 是否成功: true, 响应时间: 157ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:43:32.240",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"d507a6b484324aedab5452c21dcbcdb9","requestBody":"未记录","responseTime":157,"methodName":"getList","className":"com.genco.admin.controller.SystemUserLevelController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:43:32.476",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_square]，cost time：392580300 ns，cost：392 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:43:32.477",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: cb3ce49fe3454ae49c7fa480a03825e0, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 396ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:43:32.478",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"cb3ce49fe3454ae49c7fa480a03825e0","requestBody":"未记录","responseTime":396,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:43:36.082",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-3",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Tue Aug 05 09:43:35 CST 2025 至 Fri Aug 08 09:43:35 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:29.717",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 81476019bb2147ebb3fd6722b5e48e6b, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, URI: /api/admin/getAdminInfoByToken, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:29.717",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"81476019bb2147ebb3fd6722b5e48e6b","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getAdminInfo","requestParams":{"request":{"temp":"1754617469","token":"0703ac2ae5d44addba7e1a54f5720092"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getAdminInfoByToken","url":"http://127.0.0.1:20000/api/admin/getAdminInfoByToken"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getAdminInfoByToken"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:29.718",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getAdminInfo()，prams：[]，cost time：54100 ns，cost：0 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:29.719",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 81476019bb2147ebb3fd6722b5e48e6b, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, 是否成功: true, 响应时间: 2ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:29.719",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"81476019bb2147ebb3fd6722b5e48e6b","requestBody":"未记录","responseTime":2,"methodName":"getAdminInfo","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:29.782",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 64f6afab0a9a44629ab683fc09f05864, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, URI: /api/admin/getMenus, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:29.783",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"64f6afab0a9a44629ab683fc09f05864","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getMenus","requestParams":{"request":{"temp":"1754617469"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getMenus","url":"http://127.0.0.1:20000/api/admin/getMenus"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getMenus"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:30.054",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getMenus()，prams：[]，cost time：270702300 ns，cost：270 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:30.056",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 64f6afab0a9a44629ab683fc09f05864, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, 是否成功: true, 响应时间: 273ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:30.056",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"64f6afab0a9a44629ab683fc09f05864","requestBody":"未记录","responseTime":273,"methodName":"getMenus","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:30.126",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 6403adca27a74fe9a96a089a1419d9ae, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:30.127",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 6d814474040645978a889feed3a251f4, 类名: SystemUserLevelController, 方法名: getList, 用户ID: 1, URI: /api/admin/system/user/level/list, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:30.127",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"6403adca27a74fe9a96a089a1419d9ae","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754617470","key":"site_logo_lefttop"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:30.127",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"6d814474040645978a889feed3a251f4","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getList","requestParams":{"request":{"temp":"1754617470"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/user/level/list","url":"http://127.0.0.1:20000/api/admin/system/user/level/list"},"className":"com.genco.admin.controller.SystemUserLevelController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/user/level/list"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:30.127",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-12",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 16537b0c41fc4078a15bfed2a7cd8343, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:30.127",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-12",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"16537b0c41fc4078a15bfed2a7cd8343","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754617470","key":"site_logo_square"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:30.240",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-12",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_square]，cost time：113215700 ns，cost：113 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:30.242",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-12",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 16537b0c41fc4078a15bfed2a7cd8343, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 115ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:30.242",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-12",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"16537b0c41fc4078a15bfed2a7cd8343","requestBody":"未记录","responseTime":115,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:30.368",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemUserLevelController.getList()，prams：[]，cost time：240808300 ns，cost：240 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:30.370",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 6d814474040645978a889feed3a251f4, 类名: SystemUserLevelController, 方法名: getList, 用户ID: 1, 是否成功: true, 响应时间: 243ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:30.370",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"6d814474040645978a889feed3a251f4","requestBody":"未记录","responseTime":243,"methodName":"getList","className":"com.genco.admin.controller.SystemUserLevelController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:30.404",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_lefttop]，cost time：276020700 ns，cost：276 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:30.405",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 6403adca27a74fe9a96a089a1419d9ae, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 279ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:30.405",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"6403adca27a74fe9a96a089a1419d9ae","requestBody":"未记录","responseTime":279,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:37.280",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-26",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Tue Aug 05 09:44:36 CST 2025 至 Fri Aug 08 09:44:36 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:37.346",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 1c80fb06aceb4056818d6672f8768f20, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, URI: /api/admin/getAdminInfoByToken, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:37.347",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"1c80fb06aceb4056818d6672f8768f20","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getAdminInfo","requestParams":{"request":{"temp":"1754617477","token":"0703ac2ae5d44addba7e1a54f5720092"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getAdminInfoByToken","url":"http://127.0.0.1:20000/api/admin/getAdminInfoByToken"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getAdminInfoByToken"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:37.348",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getAdminInfo()，prams：[]，cost time：56400 ns，cost：0 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:37.350",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 1c80fb06aceb4056818d6672f8768f20, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, 是否成功: true, 响应时间: 4ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:37.350",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"1c80fb06aceb4056818d6672f8768f20","requestBody":"未记录","responseTime":4,"methodName":"getAdminInfo","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:37.359",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: ecd1d6c4bd2f4862a81118f71981decc, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, URI: /api/admin/getMenus, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:37.359",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"ecd1d6c4bd2f4862a81118f71981decc","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getMenus","requestParams":{"request":{"temp":"1754617477"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getMenus","url":"http://127.0.0.1:20000/api/admin/getMenus"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getMenus"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:37.591",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getMenus()，prams：[]，cost time：231304000 ns，cost：231 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:37.592",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: ecd1d6c4bd2f4862a81118f71981decc, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, 是否成功: true, 响应时间: 234ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:37.592",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"ecd1d6c4bd2f4862a81118f71981decc","requestBody":"未记录","responseTime":234,"methodName":"getMenus","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:37.653",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: ********************************, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:37.653",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"********************************","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754617477","key":"site_logo_square"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:37.654",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-24",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: fe6300034f684098a7ee806dfd983310, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:37.654",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-22",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 3a7cde3807ca46939f5a9fd4b006f4ee, 类名: SystemUserLevelController, 方法名: getList, 用户ID: 1, URI: /api/admin/system/user/level/list, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:37.654",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-24",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"fe6300034f684098a7ee806dfd983310","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754617477","key":"site_logo_lefttop"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:37.654",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-22",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"3a7cde3807ca46939f5a9fd4b006f4ee","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getList","requestParams":{"request":{"temp":"1754617477"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/user/level/list","url":"http://127.0.0.1:20000/api/admin/system/user/level/list"},"className":"com.genco.admin.controller.SystemUserLevelController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/user/level/list"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:37.732",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-22",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemUserLevelController.getList()，prams：[]，cost time：78635200 ns，cost：78 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:37.735",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-22",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 3a7cde3807ca46939f5a9fd4b006f4ee, 类名: SystemUserLevelController, 方法名: getList, 用户ID: 1, 是否成功: true, 响应时间: 81ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:37.735",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-22",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"3a7cde3807ca46939f5a9fd4b006f4ee","requestBody":"未记录","responseTime":81,"methodName":"getList","className":"com.genco.admin.controller.SystemUserLevelController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:37.883",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_square]，cost time：230018400 ns，cost：230 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:37.885",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: ********************************, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 231ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:37.885",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"********************************","requestBody":"未记录","responseTime":231,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:37.918",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-24",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_lefttop]，cost time：264620800 ns，cost：264 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:37.920",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-24",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: fe6300034f684098a7ee806dfd983310, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 266ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:37.920",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-24",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"fe6300034f684098a7ee806dfd983310","requestBody":"未记录","responseTime":266,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:38.909",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-25",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: f0946327825749feb65b18c7178cc1f8, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, URI: /api/admin/getAdminInfoByToken, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:38.909",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-25",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"f0946327825749feb65b18c7178cc1f8","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getAdminInfo","requestParams":{"request":{"temp":"1754617478","token":"0703ac2ae5d44addba7e1a54f5720092"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getAdminInfoByToken","url":"http://127.0.0.1:20000/api/admin/getAdminInfoByToken"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getAdminInfoByToken"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:38.910",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-25",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getAdminInfo()，prams：[]，cost time：58700 ns，cost：0 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:38.912",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-25",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: f0946327825749feb65b18c7178cc1f8, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, 是否成功: true, 响应时间: 2ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:38.912",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-25",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"f0946327825749feb65b18c7178cc1f8","requestBody":"未记录","responseTime":2,"methodName":"getAdminInfo","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:38.922",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 2acb05681d03490c91ae757c6defc00d, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, URI: /api/admin/getMenus, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:38.923",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"2acb05681d03490c91ae757c6defc00d","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getMenus","requestParams":{"request":{"temp":"1754617478"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getMenus","url":"http://127.0.0.1:20000/api/admin/getMenus"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getMenus"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:39.187",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getMenus()，prams：[]，cost time：263005200 ns，cost：263 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:39.188",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 2acb05681d03490c91ae757c6defc00d, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, 是否成功: true, 响应时间: 266ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:39.189",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"2acb05681d03490c91ae757c6defc00d","requestBody":"未记录","responseTime":266,"methodName":"getMenus","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:39.246",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 852759a6e5914ec98676a81e770c0a52, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:39.246",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-4",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 8068577a9db2428cb7c9a153429e929d, 类名: SystemUserLevelController, 方法名: getList, 用户ID: 1, URI: /api/admin/system/user/level/list, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:39.246",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"852759a6e5914ec98676a81e770c0a52","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754617479","key":"site_logo_lefttop"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:39.246",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 6d26679ccdf14b3b91f69a37e55dac32, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:39.246",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-4",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"8068577a9db2428cb7c9a153429e929d","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getList","requestParams":{"request":{"temp":"1754617479"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/user/level/list","url":"http://127.0.0.1:20000/api/admin/system/user/level/list"},"className":"com.genco.admin.controller.SystemUserLevelController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/user/level/list"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:39.246",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"6d26679ccdf14b3b91f69a37e55dac32","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754617479","key":"site_logo_square"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:39.329",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_square]，cost time：81569300 ns，cost：81 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:39.330",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 6d26679ccdf14b3b91f69a37e55dac32, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 85ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:39.331",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"6d26679ccdf14b3b91f69a37e55dac32","requestBody":"未记录","responseTime":85,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:39.477",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_lefttop]，cost time：229187700 ns，cost：229 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:39.479",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 852759a6e5914ec98676a81e770c0a52, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 233ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:39.479",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"852759a6e5914ec98676a81e770c0a52","requestBody":"未记录","responseTime":233,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:39.507",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-4",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemUserLevelController.getList()，prams：[]，cost time：259935000 ns，cost：259 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:39.508",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-4",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 8068577a9db2428cb7c9a153429e929d, 类名: SystemUserLevelController, 方法名: getList, 用户ID: 1, 是否成功: true, 响应时间: 262ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:39.508",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-4",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"8068577a9db2428cb7c9a153429e929d","requestBody":"未记录","responseTime":262,"methodName":"getList","className":"com.genco.admin.controller.SystemUserLevelController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:35.112",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-12",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 286dff161d90400789e31fcd5fe39f52, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, URI: /api/admin/getAdminInfoByToken, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:35.112",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-12",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"286dff161d90400789e31fcd5fe39f52","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getAdminInfo","requestParams":{"request":{"temp":"1754617535","token":"0703ac2ae5d44addba7e1a54f5720092"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getAdminInfoByToken","url":"http://127.0.0.1:20000/api/admin/getAdminInfoByToken"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getAdminInfoByToken"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:35.113",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-12",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getAdminInfo()，prams：[]，cost time：65600 ns，cost：0 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:35.115",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-12",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 286dff161d90400789e31fcd5fe39f52, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, 是否成功: true, 响应时间: 4ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:35.115",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-12",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"286dff161d90400789e31fcd5fe39f52","requestBody":"未记录","responseTime":4,"methodName":"getAdminInfo","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:35.152",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: d5b372509d1543298d97e31491d3c6a2, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, URI: /api/admin/getMenus, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:35.152",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"d5b372509d1543298d97e31491d3c6a2","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getMenus","requestParams":{"request":{"temp":"1754617535"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getMenus","url":"http://127.0.0.1:20000/api/admin/getMenus"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getMenus"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:35.420",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getMenus()，prams：[]，cost time：267803200 ns，cost：267 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:35.423",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: d5b372509d1543298d97e31491d3c6a2, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, 是否成功: true, 响应时间: 271ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:35.423",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"d5b372509d1543298d97e31491d3c6a2","requestBody":"未记录","responseTime":271,"methodName":"getMenus","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:35.684",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: a286dac55e8349d58a8d99bbae13cb95, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:35.684",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 49dba6e99e364185821a7be0e0218de7, 类名: SystemUserLevelController, 方法名: getList, 用户ID: 1, URI: /api/admin/system/user/level/list, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:35.684",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-16",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: c04e6a8939bf40939dac694c63bcba9e, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:35.684",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"a286dac55e8349d58a8d99bbae13cb95","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754617535","key":"site_logo_lefttop"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:35.684",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-16",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"c04e6a8939bf40939dac694c63bcba9e","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754617535","key":"site_logo_square"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:35.684",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"49dba6e99e364185821a7be0e0218de7","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getList","requestParams":{"request":{"temp":"1754617535"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/user/level/list","url":"http://127.0.0.1:20000/api/admin/system/user/level/list"},"className":"com.genco.admin.controller.SystemUserLevelController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/user/level/list"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:35.768",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-16",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_square]，cost time：83543900 ns，cost：83 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:35.770",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-16",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: c04e6a8939bf40939dac694c63bcba9e, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 86ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:35.770",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-16",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"c04e6a8939bf40939dac694c63bcba9e","requestBody":"未记录","responseTime":86,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:35.928",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemUserLevelController.getList()，prams：[]，cost time：243044300 ns，cost：243 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:35.930",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 49dba6e99e364185821a7be0e0218de7, 类名: SystemUserLevelController, 方法名: getList, 用户ID: 1, 是否成功: true, 响应时间: 246ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:35.930",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"49dba6e99e364185821a7be0e0218de7","requestBody":"未记录","responseTime":246,"methodName":"getList","className":"com.genco.admin.controller.SystemUserLevelController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:35.948",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_lefttop]，cost time：262956400 ns，cost：262 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:35.949",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: a286dac55e8349d58a8d99bbae13cb95, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 265ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:35.950",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"a286dac55e8349d58a8d99bbae13cb95","requestBody":"未记录","responseTime":265,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:38.464",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-14",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Tue Aug 05 09:45:37 CST 2025 至 Fri Aug 08 09:45:37 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:54.482",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 1a43989e72b64100b41c1ba7d287cd81, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, URI: /api/admin/getAdminInfoByToken, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:54.482",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"1a43989e72b64100b41c1ba7d287cd81","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getAdminInfo","requestParams":{"request":{"temp":"1754617554","token":"0703ac2ae5d44addba7e1a54f5720092"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getAdminInfoByToken","url":"http://127.0.0.1:20000/api/admin/getAdminInfoByToken"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getAdminInfoByToken"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:54.483",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getAdminInfo()，prams：[]，cost time：48800 ns，cost：0 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:54.485",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 1a43989e72b64100b41c1ba7d287cd81, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, 是否成功: true, 响应时间: 2ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:54.485",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"1a43989e72b64100b41c1ba7d287cd81","requestBody":"未记录","responseTime":2,"methodName":"getAdminInfo","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:54.547",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-24",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 470e7ef298c34c89a255fabd080c5e7e, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, URI: /api/admin/getMenus, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:54.548",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-24",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"470e7ef298c34c89a255fabd080c5e7e","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getMenus","requestParams":{"request":{"temp":"1754617554"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getMenus","url":"http://127.0.0.1:20000/api/admin/getMenus"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getMenus"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:54.784",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-24",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getMenus()，prams：[]，cost time：235525700 ns，cost：235 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:54.786",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-24",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 470e7ef298c34c89a255fabd080c5e7e, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, 是否成功: true, 响应时间: 239ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:54.786",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-24",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"470e7ef298c34c89a255fabd080c5e7e","requestBody":"未记录","responseTime":239,"methodName":"getMenus","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:54.846",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 950a6529ec5b4d1e9a4de222bec1dad8, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:54.846",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-25",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: d187ca8a04fb4d52a5108984e886e48c, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:54.848",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 68d99aefcadd4b43a5b234ff8509f0c5, 类名: SystemUserLevelController, 方法名: getList, 用户ID: 1, URI: /api/admin/system/user/level/list, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:54.848",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"68d99aefcadd4b43a5b234ff8509f0c5","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getList","requestParams":{"request":{"temp":"1754617554"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/user/level/list","url":"http://127.0.0.1:20000/api/admin/system/user/level/list"},"className":"com.genco.admin.controller.SystemUserLevelController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/user/level/list"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:54.848",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"950a6529ec5b4d1e9a4de222bec1dad8","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754617554","key":"site_logo_square"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:54.848",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-25",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"d187ca8a04fb4d52a5108984e886e48c","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754617554","key":"site_logo_lefttop"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"0703ac2ae5d44addba7e1a54f5720092","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:54.935",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-25",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_lefttop]，cost time：86646100 ns，cost：86 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:54.937",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-25",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: d187ca8a04fb4d52a5108984e886e48c, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 90ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:54.937",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-25",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"d187ca8a04fb4d52a5108984e886e48c","requestBody":"未记录","responseTime":90,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:55.086",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_square]，cost time：237197000 ns，cost：237 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:55.088",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 950a6529ec5b4d1e9a4de222bec1dad8, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 241ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:55.088",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"950a6529ec5b4d1e9a4de222bec1dad8","requestBody":"未记录","responseTime":241,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:55.118",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemUserLevelController.getList()，prams：[]，cost time：269524600 ns，cost：269 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:55.119",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 68d99aefcadd4b43a5b234ff8509f0c5, 类名: SystemUserLevelController, 方法名: getList, 用户ID: 1, 是否成功: true, 响应时间: 273ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:55.119",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"68d99aefcadd4b43a5b234ff8509f0c5","requestBody":"未记录","responseTime":273,"methodName":"getList","className":"com.genco.admin.controller.SystemUserLevelController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:46:39.838",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-29",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Tue Aug 05 09:46:39 CST 2025 至 Fri Aug 08 09:46:39 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:47:41.168",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-1",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Tue Aug 05 09:47:40 CST 2025 至 Fri Aug 08 09:47:40 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:48:42.505",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-17",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Tue Aug 05 09:48:41 CST 2025 至 Fri Aug 08 09:48:41 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:49:43.828",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-5",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Tue Aug 05 09:49:43 CST 2025 至 Fri Aug 08 09:49:43 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:50:00.539",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-10",
                    "class": "c.g.a.task.tiktok.TiktokTokenRefreshTask",
                    "message": "---TiktokTokenRefreshTask task------produce Data with fixed rate task: Execution Time - Fri Aug 08 09:50:00 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:50:00.803",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-10",
                    "class": "c.g.a.task.tiktok.TiktokTokenRefreshTask",
                    "message": "Tiktok access_token 未到刷新时间，当前时间: 1754617800，过期时间: 1781838424" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:50:45.206",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-2",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Tue Aug 05 09:50:44 CST 2025 至 Fri Aug 08 09:50:44 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:51:13.031",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-4",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 2148807b89a34cd78a85932963f42618, 类名: AdminLoginController, 方法名: getLoginPic, 用户ID: null, URI: /api/admin/getLoginPic, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:51:13.031",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-6",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 05d8545a60764b49a63b2bdedb920edd, 类名: ValidateCodeController, 方法名: get, 用户ID: null, URI: /api/admin/validate/code/get, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:51:13.032",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-4",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"2148807b89a34cd78a85932963f42618","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getLoginPic","requestParams":{"request":{"temp":"**********"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getLoginPic","url":"http://127.0.0.1:20000/api/admin/getLoginPic"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","uri":"/api/admin/getLoginPic"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:51:13.032",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-6",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"05d8545a60764b49a63b2bdedb920edd","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"get","requestParams":{"request":{"temp":"**********"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/validate/code/get","url":"http://127.0.0.1:20000/api/admin/validate/code/get"},"className":"com.genco.admin.controller.ValidateCodeController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","uri":"/api/admin/validate/code/get"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:51:13.463",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-6",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.ValidateCodeController.get()，prams：[]，cost time：430740000 ns，cost：430 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:51:13.465",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-6",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 05d8545a60764b49a63b2bdedb920edd, 类名: ValidateCodeController, 方法名: get, 用户ID: null, 是否成功: true, 响应时间: 434ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:51:13.465",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-6",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"05d8545a60764b49a63b2bdedb920edd","requestBody":"未记录","responseTime":434,"methodName":"get","className":"com.genco.admin.controller.ValidateCodeController","isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:51:14.399",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-4",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getLoginPic()，prams：[]，cost time：********** ns，cost：1366 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:51:14.401",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-4",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 2148807b89a34cd78a85932963f42618, 类名: AdminLoginController, 方法名: getLoginPic, 用户ID: null, 是否成功: true, 响应时间: 1370ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:51:14.401",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-4",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"2148807b89a34cd78a85932963f42618","requestBody":"未记录","responseTime":1370,"methodName":"getLoginPic","className":"com.genco.admin.controller.AdminLoginController","isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:51:46.546",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-22",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Tue Aug 05 09:51:46 CST 2025 至 Fri Aug 08 09:51:46 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:52:47.882",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-12",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Tue Aug 05 09:52:47 CST 2025 至 Fri Aug 08 09:52:47 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:53:49.234",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-25",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Tue Aug 05 09:53:48 CST 2025 至 Fri Aug 08 09:53:48 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:54:50.559",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-7",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Tue Aug 05 09:54:50 CST 2025 至 Fri Aug 08 09:54:50 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:54:55.392",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 70fb623197a242148f93a08fa6ed36c7, 类名: AdminLoginController, 方法名: SystemAdminLogin, 用户ID: null, URI: /api/admin/login, 方法: POST" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:54:55.393",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"70fb623197a242148f93a08fa6ed36c7","method":"POST","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"SystemAdminLogin","requestParams":{"request":{},"method":"POST","request_filter":{},"header":{"sec-fetch-mode":"cors","content-length":"107","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","content-type":"application/json","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/login","url":"http://127.0.0.1:20000/api/admin/login"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","uri":"/api/admin/login"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:54:57.832",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.SystemAdminLogin(SystemAdminLoginRequest,HttpServletRequest)，prams：[SystemAdminLoginRequest(account=admin, pwd=BzX36YjvNR8o, key=482c5f26bae7d1c5dd3dfa9a35077579, code=yfmy), SecurityContextHolderAwareRequestWrapper[ org.springframework.security.web.header.HeaderWriterFilter$HeaderWriterRequest@520209b8]]，cost time：********** ns，cost：2437 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:54:57.834",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 70fb623197a242148f93a08fa6ed36c7, 类名: AdminLoginController, 方法名: SystemAdminLogin, 用户ID: null, 是否成功: true, 响应时间: 2442ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:54:57.834",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"70fb623197a242148f93a08fa6ed36c7","requestBody":"未记录","responseTime":2442,"methodName":"SystemAdminLogin","className":"com.genco.admin.controller.AdminLoginController","isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:54:57.850",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-10",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 657fd21ec9d44c8ab62a1f26c506818c, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, URI: /api/admin/getAdminInfoByToken, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:54:57.850",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-15",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: ********************************, 类名: SystemStoreStaffController, 方法名: getList, 用户ID: 1, URI: /api/admin/system/store/staff/list, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:54:57.850",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-10",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"657fd21ec9d44c8ab62a1f26c506818c","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getAdminInfo","requestParams":{"request":{"temp":"1754618097","token":"955e0d3c20f942e58c435436ed857b66"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"955e0d3c20f942e58c435436ed857b66","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getAdminInfoByToken","url":"http://127.0.0.1:20000/api/admin/getAdminInfoByToken"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getAdminInfoByToken"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:54:57.850",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-15",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"********************************","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getList","requestParams":{"request":{"temp":"1754618097","limit":"9999","page":"1"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"955e0d3c20f942e58c435436ed857b66","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/store/staff/list","url":"http://127.0.0.1:20000/api/admin/system/store/staff/list"},"className":"com.genco.admin.controller.SystemStoreStaffController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/store/staff/list"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:54:57.851",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-10",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getAdminInfo()，prams：[]，cost time：447700 ns，cost：0 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:54:57.853",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-10",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 657fd21ec9d44c8ab62a1f26c506818c, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, 是否成功: true, 响应时间: 3ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:54:57.853",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-10",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"657fd21ec9d44c8ab62a1f26c506818c","requestBody":"未记录","responseTime":3,"methodName":"getAdminInfo","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:54:57.863",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: d160f5b50d8046eca53760002400d165, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, URI: /api/admin/getMenus, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:54:57.863",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"d160f5b50d8046eca53760002400d165","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getMenus","requestParams":{"request":{"temp":"1754618097"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"955e0d3c20f942e58c435436ed857b66","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getMenus","url":"http://127.0.0.1:20000/api/admin/getMenus"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getMenus"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:54:58.111",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-15",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemStoreStaffController.getList(Integer,PageParamRequest)，prams：[0, PageParamRequest(page=1, limit=9999)]，cost time：260126000 ns，cost：260 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:54:58.113",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-15",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: ********************************, 类名: SystemStoreStaffController, 方法名: getList, 用户ID: 1, 是否成功: true, 响应时间: 263ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:54:58.114",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-15",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"********************************","requestBody":"未记录","responseTime":263,"methodName":"getList","className":"com.genco.admin.controller.SystemStoreStaffController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:54:58.335",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getMenus()，prams：[]，cost time：470610600 ns，cost：470 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:54:58.336",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: d160f5b50d8046eca53760002400d165, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, 是否成功: true, 响应时间: 474ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:54:58.336",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"d160f5b50d8046eca53760002400d165","requestBody":"未记录","responseTime":474,"methodName":"getMenus","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:54:58.416",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-19",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: e2ca1bd252014405bb2fe19fed5b3277, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:54:58.416",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-22",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: e8cffe2c29a143bd8e8905bf1227ee79, 类名: SystemConfigController, 方法名: info, 用户ID: 1, URI: /api/admin/system/config/info, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:54:58.416",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 0391619c32df45808745518cb00ecfd5, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:54:58.416",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-19",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"e2ca1bd252014405bb2fe19fed5b3277","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754618098","key":"site_logo_lefttop"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"955e0d3c20f942e58c435436ed857b66","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:54:58.416",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-22",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"e8cffe2c29a143bd8e8905bf1227ee79","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"info","requestParams":{"request":{"formId":"100","temp":"1754618098"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"955e0d3c20f942e58c435436ed857b66","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/info","url":"http://127.0.0.1:20000/api/admin/system/config/info"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/info"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:54:58.416",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"0391619c32df45808745518cb00ecfd5","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754618098","key":"site_logo_square"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"955e0d3c20f942e58c435436ed857b66","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:54:58.581",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-19",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_lefttop]，cost time：163186200 ns，cost：163 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:54:58.583",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-19",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: e2ca1bd252014405bb2fe19fed5b3277, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 167ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:54:58.583",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-19",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"e2ca1bd252014405bb2fe19fed5b3277","requestBody":"未记录","responseTime":167,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:54:58.648",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-22",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.info(Integer)，prams：[100]，cost time：230517800 ns，cost：230 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:54:58.650",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-22",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: e8cffe2c29a143bd8e8905bf1227ee79, 类名: SystemConfigController, 方法名: info, 用户ID: 1, 是否成功: true, 响应时间: 234ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:54:58.650",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-22",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"e8cffe2c29a143bd8e8905bf1227ee79","requestBody":"未记录","responseTime":234,"methodName":"info","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:54:58.678",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_square]，cost time：260720800 ns，cost：260 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:54:58.681",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 0391619c32df45808745518cb00ecfd5, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 265ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:54:58.681",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"0391619c32df45808745518cb00ecfd5","requestBody":"未记录","responseTime":265,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:01.306",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-26",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 0ca181127b434dd3b5452f48017ba031, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, URI: /api/admin/getAdminInfoByToken, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:01.306",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-26",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"0ca181127b434dd3b5452f48017ba031","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getAdminInfo","requestParams":{"request":{"temp":"1754618101","token":"955e0d3c20f942e58c435436ed857b66"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"955e0d3c20f942e58c435436ed857b66","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getAdminInfoByToken","url":"http://127.0.0.1:20000/api/admin/getAdminInfoByToken"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getAdminInfoByToken"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:01.307",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-26",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getAdminInfo()，prams：[]，cost time：94700 ns，cost：0 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:01.308",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-26",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 0ca181127b434dd3b5452f48017ba031, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, 是否成功: true, 响应时间: 2ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:01.308",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-26",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"0ca181127b434dd3b5452f48017ba031","requestBody":"未记录","responseTime":2,"methodName":"getAdminInfo","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:01.318",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 3706b4b8e4e847bc83ac018b3f38ec92, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, URI: /api/admin/getMenus, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:01.318",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"3706b4b8e4e847bc83ac018b3f38ec92","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getMenus","requestParams":{"request":{"temp":"1754618101"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"955e0d3c20f942e58c435436ed857b66","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getMenus","url":"http://127.0.0.1:20000/api/admin/getMenus"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getMenus"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:01.584",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getMenus()，prams：[]，cost time：266051400 ns，cost：266 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:01.586",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 3706b4b8e4e847bc83ac018b3f38ec92, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, 是否成功: true, 响应时间: 268ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:01.586",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"3706b4b8e4e847bc83ac018b3f38ec92","requestBody":"未记录","responseTime":268,"methodName":"getMenus","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:01.638",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-30",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: be32f614ed66468986daced25bfba1ab, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:01.638",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 2dc5f17291eb422fa68dad7f8552d7f2, 类名: SystemConfigController, 方法名: info, 用户ID: 1, URI: /api/admin/system/config/info, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:01.638",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 4f8f447b2b8e4ef29426be73fec72fd5, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:01.638",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"2dc5f17291eb422fa68dad7f8552d7f2","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"info","requestParams":{"request":{"formId":"100","temp":"1754618101"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"955e0d3c20f942e58c435436ed857b66","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/info","url":"http://127.0.0.1:20000/api/admin/system/config/info"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/info"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:01.638",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-30",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"be32f614ed66468986daced25bfba1ab","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754618101","key":"site_logo_lefttop"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"955e0d3c20f942e58c435436ed857b66","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:01.638",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"4f8f447b2b8e4ef29426be73fec72fd5","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754618101","key":"site_logo_square"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"955e0d3c20f942e58c435436ed857b66","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:01.717",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-30",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_lefttop]，cost time：77912900 ns，cost：77 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:01.719",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-30",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: be32f614ed66468986daced25bfba1ab, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 82ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:01.719",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-30",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"be32f614ed66468986daced25bfba1ab","requestBody":"未记录","responseTime":82,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:01.869",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_square]，cost time：229912400 ns，cost：229 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:01.872",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 4f8f447b2b8e4ef29426be73fec72fd5, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 234ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:01.872",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"4f8f447b2b8e4ef29426be73fec72fd5","requestBody":"未记录","responseTime":234,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:01.900",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.info(Integer)，prams：[100]，cost time：261069700 ns，cost：261 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:01.902",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 2dc5f17291eb422fa68dad7f8552d7f2, 类名: SystemConfigController, 方法名: info, 用户ID: 1, 是否成功: true, 响应时间: 265ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:01.902",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"2dc5f17291eb422fa68dad7f8552d7f2","requestBody":"未记录","responseTime":265,"methodName":"info","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:36.386",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-8",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: bbc522d1953542a595cbe03d55f8419c, 类名: UserRechargeController, 方法名: getList, 用户ID: 1, URI: /api/admin/user/topUpLog/list, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:36.386",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-8",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"bbc522d1953542a595cbe03d55f8419c","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getList","requestParams":{"request":{"uid":"","total":"0","temp":"**********","dateLimit":"","limit":"20","payChannel":"","bankName":"","walletCode":"","page":"1","keyword":"","rechargeType":""},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"955e0d3c20f942e58c435436ed857b66","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/user/topUpLog/list","url":"http://127.0.0.1:20000/api/admin/user/topUpLog/list"},"className":"com.genco.admin.controller.UserRechargeController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/user/topUpLog/list"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:36.387",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: dc71b15c491a48e7958299c2e0913078, 类名: UserExtractController, 方法名: getExtractBank, 用户ID: 1, URI: /api/admin/finance/apply/extract/bank, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:36.387",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"dc71b15c491a48e7958299c2e0913078","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getExtractBank","requestParams":{"request":{"temp":"**********"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"955e0d3c20f942e58c435436ed857b66","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/finance/apply/extract/bank","url":"http://127.0.0.1:20000/api/admin/finance/apply/extract/bank"},"className":"com.genco.admin.controller.UserExtractController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/finance/apply/extract/bank"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:36.662",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.UserExtractController.getExtractBank()，prams：[]，cost time：********* ns，cost：273 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:36.666",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: dc71b15c491a48e7958299c2e0913078, 类名: UserExtractController, 方法名: getExtractBank, 用户ID: 1, 是否成功: true, 响应时间: 280ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:36.666",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"dc71b15c491a48e7958299c2e0913078","requestBody":"未记录","responseTime":280,"methodName":"getExtractBank","className":"com.genco.admin.controller.UserExtractController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:37.191",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-8",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.UserRechargeController.getList(UserRechargeSearchRequest,PageParamRequest)，prams：[UserRechargeSearchRequest(keywords=null, dateLimit=, uid=null, rechargeType=, payChannel=), PageParamRequest(page=1, limit=20)]，cost time：********* ns，cost：786 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:37.195",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-8",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: bbc522d1953542a595cbe03d55f8419c, 类名: UserRechargeController, 方法名: getList, 用户ID: 1, 是否成功: true, 响应时间: 810ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:37.195",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-8",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"bbc522d1953542a595cbe03d55f8419c","requestBody":"未记录","responseTime":810,"methodName":"getList","className":"com.genco.admin.controller.UserRechargeController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:38.971",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: c696f17526e8419481b3941c30ea8e41, 类名: UserController, 方法名: getLevelUpgradeOrderList, 用户ID: 1, URI: /api/admin/user/level/upgrade/list, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:38.972",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"c696f17526e8419481b3941c30ea8e41","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getLevelUpgradeOrderList","requestParams":{"request":{"temp":"1754618138","limit":"20","page":"1"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"955e0d3c20f942e58c435436ed857b66","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/user/level/upgrade/list","url":"http://127.0.0.1:20000/api/admin/user/level/upgrade/list"},"className":"com.genco.admin.controller.UserController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/user/level/upgrade/list"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:39.256",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.UserController.getLevelUpgradeOrderList(UserLevelUpgradeOrderSearchRequest,PageParamRequest)，prams：[UserLevelUpgradeOrderSearchRequest(orderNo=null, orderStatus=null, uid=null, paymentMethod=null, startTime=null, endTime=null), PageParamRequest(page=1, limit=20)]，cost time：276064800 ns，cost：276 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:39.258",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: c696f17526e8419481b3941c30ea8e41, 类名: UserController, 方法名: getLevelUpgradeOrderList, 用户ID: 1, 是否成功: true, 响应时间: 288ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:39.258",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"c696f17526e8419481b3941c30ea8e41","requestBody":"未记录","responseTime":288,"methodName":"getLevelUpgradeOrderList","className":"com.genco.admin.controller.UserController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:40.754",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-14",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: efe5c7b881e44c60be9f0a87b2041e92, 类名: SystemUserLevelController, 方法名: getList, 用户ID: 1, URI: /api/admin/system/user/level/list, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:40.754",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-14",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"efe5c7b881e44c60be9f0a87b2041e92","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getList","requestParams":{"request":{"temp":"1754618140"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"955e0d3c20f942e58c435436ed857b66","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/user/level/list","url":"http://127.0.0.1:20000/api/admin/system/user/level/list"},"className":"com.genco.admin.controller.SystemUserLevelController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/user/level/list"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:40.985",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-14",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemUserLevelController.getList()，prams：[]，cost time：229972500 ns，cost：229 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:40.988",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-14",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: efe5c7b881e44c60be9f0a87b2041e92, 类名: SystemUserLevelController, 方法名: getList, 用户ID: 1, 是否成功: true, 响应时间: 233ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:40.988",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-14",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"efe5c7b881e44c60be9f0a87b2041e92","requestBody":"未记录","responseTime":233,"methodName":"getList","className":"com.genco.admin.controller.SystemUserLevelController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:41.472",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-16",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: e5f50c8c62b54a80827c211dbca84c88, 类名: UserController, 方法名: getList, 用户ID: 1, URI: /api/admin/user/list, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:41.473",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-16",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"e5f50c8c62b54a80827c211dbca84c88","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getList","requestParams":{"request":{"total":"0","temp":"1754618141","keywords":"","level":"","limit":"20","page":"1"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"955e0d3c20f942e58c435436ed857b66","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/user/list","url":"http://127.0.0.1:20000/api/admin/user/list"},"className":"com.genco.admin.controller.UserController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/user/list"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:42.723",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-17",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: ca66a3004f4f492da71da2c5c9d71f22, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, URI: /api/admin/getAdminInfoByToken, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:42.723",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-17",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"ca66a3004f4f492da71da2c5c9d71f22","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getAdminInfo","requestParams":{"request":{"temp":"1754618142","token":"955e0d3c20f942e58c435436ed857b66"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"955e0d3c20f942e58c435436ed857b66","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getAdminInfoByToken","url":"http://127.0.0.1:20000/api/admin/getAdminInfoByToken"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getAdminInfoByToken"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:42.723",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-17",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getAdminInfo()，prams：[]，cost time：57100 ns，cost：0 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:42.726",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-17",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: ca66a3004f4f492da71da2c5c9d71f22, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, 是否成功: true, 响应时间: 3ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:42.726",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-17",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"ca66a3004f4f492da71da2c5c9d71f22","requestBody":"未记录","responseTime":3,"methodName":"getAdminInfo","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:42.788",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-19",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 48cdff5c50d2440c97e0962d3f22046f, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, URI: /api/admin/getMenus, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:42.788",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-19",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"48cdff5c50d2440c97e0962d3f22046f","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getMenus","requestParams":{"request":{"temp":"1754618142"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"955e0d3c20f942e58c435436ed857b66","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getMenus","url":"http://127.0.0.1:20000/api/admin/getMenus"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getMenus"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:43.058",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-19",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getMenus()，prams：[]，cost time：270503200 ns，cost：270 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:43.061",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-19",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 48cdff5c50d2440c97e0962d3f22046f, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, 是否成功: true, 响应时间: 272ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:43.062",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-19",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"48cdff5c50d2440c97e0962d3f22046f","requestBody":"未记录","responseTime":272,"methodName":"getMenus","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:43.134",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-27",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 5ddc9482e32c4df6b0513f1befbcf241, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:43.134",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-26",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: af3a79a27dbc426b99d18d7c068ac736, 类名: SystemUserLevelController, 方法名: getList, 用户ID: 1, URI: /api/admin/system/user/level/list, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:43.134",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-23",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 2595140011af41de96d9969ba61c0201, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:43.135",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-27",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"5ddc9482e32c4df6b0513f1befbcf241","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754618143","key":"site_logo_square"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"955e0d3c20f942e58c435436ed857b66","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:43.134",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-26",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"af3a79a27dbc426b99d18d7c068ac736","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getList","requestParams":{"request":{"temp":"1754618143"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"955e0d3c20f942e58c435436ed857b66","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/user/level/list","url":"http://127.0.0.1:20000/api/admin/system/user/level/list"},"className":"com.genco.admin.controller.SystemUserLevelController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/user/level/list"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:43.134",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-23",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"2595140011af41de96d9969ba61c0201","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754618143","key":"site_logo_lefttop"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"955e0d3c20f942e58c435436ed857b66","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:43.218",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-27",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_square]，cost time：83484100 ns，cost：83 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:43.220",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-27",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 5ddc9482e32c4df6b0513f1befbcf241, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 87ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:43.221",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-27",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"5ddc9482e32c4df6b0513f1befbcf241","requestBody":"未记录","responseTime":87,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:43.375",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-23",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_lefttop]，cost time：239728300 ns，cost：239 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:43.377",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-23",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 2595140011af41de96d9969ba61c0201, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 243ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:43.377",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-23",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"2595140011af41de96d9969ba61c0201","requestBody":"未记录","responseTime":243,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:43.401",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-26",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemUserLevelController.getList()，prams：[]，cost time：266196100 ns，cost：266 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:43.403",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-26",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: af3a79a27dbc426b99d18d7c068ac736, 类名: SystemUserLevelController, 方法名: getList, 用户ID: 1, 是否成功: true, 响应时间: 269ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:43.403",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-26",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"af3a79a27dbc426b99d18d7c068ac736","requestBody":"未记录","responseTime":269,"methodName":"getList","className":"com.genco.admin.controller.SystemUserLevelController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:43.531",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-16",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.UserController.getList(UserSearchRequest,PageParamRequest)，prams：[UserSearchRequest(keywords=, phone=null, nickname=null, dateLimit=null, groupId=null, labelId=null, userType=null, status=null, isPromoter=null, payCount=null, level=, accessType=0, country=null, province=null, city=null, sex=null), PageParamRequest(page=1, limit=20)]，cost time：2037597100 ns，cost：2037 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:43.537",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-16",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: e5f50c8c62b54a80827c211dbca84c88, 类名: UserController, 方法名: getList, 用户ID: 1, 是否成功: true, 响应时间: 2064ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:43.537",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-16",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"e5f50c8c62b54a80827c211dbca84c88","requestBody":"未记录","responseTime":2064,"methodName":"getList","className":"com.genco.admin.controller.UserController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:43.963",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: ba077d2bd84e4242b2f1fc48fced95b4, 类名: UserController, 方法名: getList, 用户ID: 1, URI: /api/admin/user/list, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:43.963",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"ba077d2bd84e4242b2f1fc48fced95b4","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getList","requestParams":{"request":{"total":"0","temp":"1754618143","keywords":"","level":"","limit":"20","page":"1"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"955e0d3c20f942e58c435436ed857b66","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/user/list","url":"http://127.0.0.1:20000/api/admin/user/list"},"className":"com.genco.admin.controller.UserController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/user/list"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:46.885",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.UserController.getList(UserSearchRequest,PageParamRequest)，prams：[UserSearchRequest(keywords=, phone=null, nickname=null, dateLimit=null, groupId=null, labelId=null, userType=null, status=null, isPromoter=null, payCount=null, level=, accessType=0, country=null, province=null, city=null, sex=null), PageParamRequest(page=1, limit=20)]，cost time：2920276400 ns，cost：2920 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:46.887",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: ba077d2bd84e4242b2f1fc48fced95b4, 类名: UserController, 方法名: getList, 用户ID: 1, 是否成功: true, 响应时间: 2925ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:46.889",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"ba077d2bd84e4242b2f1fc48fced95b4","requestBody":"未记录","responseTime":2925,"methodName":"getList","className":"com.genco.admin.controller.UserController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
