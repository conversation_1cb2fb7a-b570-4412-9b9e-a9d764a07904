{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:20:03.204",
                    "level": "ERROR",
                    "thread": "crmeb-scheduled-task-pool-3",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"20250808092003C4D4D16A433F194153BA"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 01:20:03 GMT], expires=[Fri, 08 Aug 2025 01:20:03 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=31, origin; dur=12, inner; dur=3], tt_stable=[0], x-akamai-request-id=[1dc67391.1d33e15], x-cache=[TCP_MISS from a23-32-20-53.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-cache-remote=[TCP_MISS from a23-221-208-240.deploy.akamaitechnologies.com (AkamaiGHost/22.2.0-44748e1838d946f91603bfea222ba018) (-)], x-origin-response-time=[12,**************], x-parent-response-time=[43,**********3], x-tt-logid=[20250808092003C4D4D16A433F194153BA], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7ee5a0b437467c2efea3c7a9a102eb7b9fa0e235b4625c51f87dc4bac5956714274a4818cb15856a3c2e69a9a9b15b4b92b074443428142352e47f4e7e16554daccee28cd7aec169f710e3687d9237f92bc93d69d4a749a15e9ac555333f978622], x-tt-trace-id=[00-250808092003C4D4D16A433F194153BA-2AA1E1636550FFC8-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}" }
                    
tiktokshop.open.sdk_java.invoke.ApiException: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"20250808092003C4D4D16A433F194153BA"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 01:20:03 GMT], expires=[Fri, 08 Aug 2025 01:20:03 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=31, origin; dur=12, inner; dur=3], tt_stable=[0], x-akamai-request-id=[1dc67391.1d33e15], x-cache=[TCP_MISS from a23-32-20-53.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-cache-remote=[TCP_MISS from a23-221-208-240.deploy.akamaitechnologies.com (AkamaiGHost/22.2.0-44748e1838d946f91603bfea222ba018) (-)], x-origin-response-time=[12,**************], x-parent-response-time=[43,**********3], x-tt-logid=[20250808092003C4D4D16A433F194153BA], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7ee5a0b437467c2efea3c7a9a102eb7b9fa0e235b4625c51f87dc4bac5956714274a4818cb15856a3c2e69a9a9b15b4b92b074443428142352e47f4e7e16554daccee28cd7aec169f710e3687d9237f92bc93d69d4a749a15e9ac555333f978622], x-tt-trace-id=[00-250808092003C4D4D16A433F194153BA-2AA1E1636550FFC8-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}
	at tiktokshop.open.sdk_java.invoke.ApiClient.handleResponse(ApiClient.java:1198)
	at tiktokshop.open.sdk_java.invoke.ApiClient.execute(ApiClient.java:1111)
	at tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api.affiliateCreator202410OrdersSearchPostWithHttpInfo(AffiliateCreatorV202410Api.java:214)
	at tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api.affiliateCreator202410OrdersSearchPost(AffiliateCreatorV202410Api.java:191)
	at com.genco.service.service.impl.TiktokOrderSyncServiceImpl.syncTiktokOrders(TiktokOrderSyncServiceImpl.java:81)
	at com.genco.admin.task.order.OrderTiktokPullTask.pullTiktokOrders(OrderTiktokPullTask.java:86)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:20:03.206",
                    "level": "ERROR",
                    "thread": "crmeb-scheduled-task-pool-3",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "TikTok订单拉取任务失败，taskId=6006, pageNo=1, err=TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"20250808092003C4D4D16A433F194153BA"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 01:20:03 GMT], expires=[Fri, 08 Aug 2025 01:20:03 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=31, origin; dur=12, inner; dur=3], tt_stable=[0], x-akamai-request-id=[1dc67391.1d33e15], x-cache=[TCP_MISS from a23-32-20-53.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-cache-remote=[TCP_MISS from a23-221-208-240.deploy.akamaitechnologies.com (AkamaiGHost/22.2.0-44748e1838d946f91603bfea222ba018) (-)], x-origin-response-time=[12,**************], x-parent-response-time=[43,**********3], x-tt-logid=[20250808092003C4D4D16A433F194153BA], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7ee5a0b437467c2efea3c7a9a102eb7b9fa0e235b4625c51f87dc4bac5956714274a4818cb15856a3c2e69a9a9b15b4b92b074443428142352e47f4e7e16554daccee28cd7aec169f710e3687d9237f92bc93d69d4a749a15e9ac555333f978622], x-tt-trace-id=[00-250808092003C4D4D16A433F194153BA-2AA1E1636550FFC8-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}" }
                    
com.genco.common.exception.CrmebException: TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"20250808092003C4D4D16A433F194153BA"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 01:20:03 GMT], expires=[Fri, 08 Aug 2025 01:20:03 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=31, origin; dur=12, inner; dur=3], tt_stable=[0], x-akamai-request-id=[1dc67391.1d33e15], x-cache=[TCP_MISS from a23-32-20-53.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-cache-remote=[TCP_MISS from a23-221-208-240.deploy.akamaitechnologies.com (AkamaiGHost/22.2.0-44748e1838d946f91603bfea222ba018) (-)], x-origin-response-time=[12,**************], x-parent-response-time=[43,**********3], x-tt-logid=[20250808092003C4D4D16A433F194153BA], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7ee5a0b437467c2efea3c7a9a102eb7b9fa0e235b4625c51f87dc4bac5956714274a4818cb15856a3c2e69a9a9b15b4b92b074443428142352e47f4e7e16554daccee28cd7aec169f710e3687d9237f92bc93d69d4a749a15e9ac555333f978622], x-tt-trace-id=[00-250808092003C4D4D16A433F194153BA-2AA1E1636550FFC8-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}
	at com.genco.service.service.impl.TiktokOrderSyncServiceImpl.syncTiktokOrders(TiktokOrderSyncServiceImpl.java:209)
	at com.genco.admin.task.order.OrderTiktokPullTask.pullTiktokOrders(OrderTiktokPullTask.java:86)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:20:26.804",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:05.221",
                    "level": "ERROR",
                    "thread": "crmeb-scheduled-task-pool-5",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"20250808092105DD5E3B7D66BACA40A89F"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 01:21:05 GMT], expires=[Fri, 08 Aug 2025 01:21:05 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=33, origin; dur=12, inner; dur=3], tt_stable=[0], x-akamai-request-id=[314589be.12485e1f], x-cache=[TCP_MISS from a104-84-150-13.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-cache-remote=[TCP_MISS from a23-200-218-28.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[12,*************], x-parent-response-time=[44,*************], x-tt-logid=[20250808092105DD5E3B7D66BACA40A89F], x-tt-oec-error-level=[-], x-tt-trace-host=[013cd85c15ef3f9fe2951b700171db7a3623f3a424d48654133f96ce47cc5c8b1dd9c143ecd5d8e0ed4a8c1bd93cff9dbc0b8b98606cac6d3358bb45788672656aa2679a10fb8ec08cbbfcfdda541a2ba671031403462437626acb77e1211a3450d2a49fffce22072d6c29f5f8db3cb806], x-tt-trace-id=[00-250808092105DD5E3B7D66BACA40A89F-4018238A615ECCA3-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}" }
                    
tiktokshop.open.sdk_java.invoke.ApiException: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"20250808092105DD5E3B7D66BACA40A89F"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 01:21:05 GMT], expires=[Fri, 08 Aug 2025 01:21:05 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=33, origin; dur=12, inner; dur=3], tt_stable=[0], x-akamai-request-id=[314589be.12485e1f], x-cache=[TCP_MISS from a104-84-150-13.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-cache-remote=[TCP_MISS from a23-200-218-28.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[12,*************], x-parent-response-time=[44,*************], x-tt-logid=[20250808092105DD5E3B7D66BACA40A89F], x-tt-oec-error-level=[-], x-tt-trace-host=[013cd85c15ef3f9fe2951b700171db7a3623f3a424d48654133f96ce47cc5c8b1dd9c143ecd5d8e0ed4a8c1bd93cff9dbc0b8b98606cac6d3358bb45788672656aa2679a10fb8ec08cbbfcfdda541a2ba671031403462437626acb77e1211a3450d2a49fffce22072d6c29f5f8db3cb806], x-tt-trace-id=[00-250808092105DD5E3B7D66BACA40A89F-4018238A615ECCA3-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}
	at tiktokshop.open.sdk_java.invoke.ApiClient.handleResponse(ApiClient.java:1198)
	at tiktokshop.open.sdk_java.invoke.ApiClient.execute(ApiClient.java:1111)
	at tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api.affiliateCreator202410OrdersSearchPostWithHttpInfo(AffiliateCreatorV202410Api.java:214)
	at tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api.affiliateCreator202410OrdersSearchPost(AffiliateCreatorV202410Api.java:191)
	at com.genco.service.service.impl.TiktokOrderSyncServiceImpl.syncTiktokOrders(TiktokOrderSyncServiceImpl.java:81)
	at com.genco.admin.task.order.OrderTiktokPullTask.pullTiktokOrders(OrderTiktokPullTask.java:86)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:05.222",
                    "level": "ERROR",
                    "thread": "crmeb-scheduled-task-pool-5",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "TikTok订单拉取任务失败，taskId=6007, pageNo=1, err=TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"20250808092105DD5E3B7D66BACA40A89F"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 01:21:05 GMT], expires=[Fri, 08 Aug 2025 01:21:05 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=33, origin; dur=12, inner; dur=3], tt_stable=[0], x-akamai-request-id=[314589be.12485e1f], x-cache=[TCP_MISS from a104-84-150-13.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-cache-remote=[TCP_MISS from a23-200-218-28.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[12,*************], x-parent-response-time=[44,*************], x-tt-logid=[20250808092105DD5E3B7D66BACA40A89F], x-tt-oec-error-level=[-], x-tt-trace-host=[013cd85c15ef3f9fe2951b700171db7a3623f3a424d48654133f96ce47cc5c8b1dd9c143ecd5d8e0ed4a8c1bd93cff9dbc0b8b98606cac6d3358bb45788672656aa2679a10fb8ec08cbbfcfdda541a2ba671031403462437626acb77e1211a3450d2a49fffce22072d6c29f5f8db3cb806], x-tt-trace-id=[00-250808092105DD5E3B7D66BACA40A89F-4018238A615ECCA3-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}" }
                    
com.genco.common.exception.CrmebException: TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"20250808092105DD5E3B7D66BACA40A89F"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 01:21:05 GMT], expires=[Fri, 08 Aug 2025 01:21:05 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=33, origin; dur=12, inner; dur=3], tt_stable=[0], x-akamai-request-id=[314589be.12485e1f], x-cache=[TCP_MISS from a104-84-150-13.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-cache-remote=[TCP_MISS from a23-200-218-28.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[12,*************], x-parent-response-time=[44,*************], x-tt-logid=[20250808092105DD5E3B7D66BACA40A89F], x-tt-oec-error-level=[-], x-tt-trace-host=[013cd85c15ef3f9fe2951b700171db7a3623f3a424d48654133f96ce47cc5c8b1dd9c143ecd5d8e0ed4a8c1bd93cff9dbc0b8b98606cac6d3358bb45788672656aa2679a10fb8ec08cbbfcfdda541a2ba671031403462437626acb77e1211a3450d2a49fffce22072d6c29f5f8db3cb806], x-tt-trace-id=[00-250808092105DD5E3B7D66BACA40A89F-4018238A615ECCA3-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}
	at com.genco.service.service.impl.TiktokOrderSyncServiceImpl.syncTiktokOrders(TiktokOrderSyncServiceImpl.java:209)
	at com.genco.admin.task.order.OrderTiktokPullTask.pullTiktokOrders(OrderTiktokPullTask.java:86)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:22:07.773",
                    "level": "ERROR",
                    "thread": "crmeb-scheduled-task-pool-3",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"2025080809220701186FB993AC1340AF6C"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 01:22:07 GMT], expires=[Fri, 08 Aug 2025 01:22:07 GMT], pragma=[no-cache], server=[TLB], server-timing=[inner; dur=5, cdn-cache; desc=MISS, edge; dur=1, origin; dur=51], tt_stable=[0], x-akamai-request-id=[107ed21c], x-cache=[TCP_MISS from a23-32-20-21.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-origin-response-time=[51,***********], x-tt-logid=[2025080809220701186FB993AC1340AF6C], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7e1f29a5042b4b937256b6a40d0261f5d649c23bab5ec18bbe6c4e11d44df6edf7bebc37111932648636429b0bba255120a554be60b23eb579a71b1c0cbf1f98f82bdd8e353445556aa44d4fdb91332ab1], x-tt-trace-id=[00-25080809220701186FB993AC1340AF6C-2F0040D55D006630-00], x-tt-trace-tag=[id=16;cdn-cache=hit;type=dyn]}" }
                    
tiktokshop.open.sdk_java.invoke.ApiException: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"2025080809220701186FB993AC1340AF6C"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 01:22:07 GMT], expires=[Fri, 08 Aug 2025 01:22:07 GMT], pragma=[no-cache], server=[TLB], server-timing=[inner; dur=5, cdn-cache; desc=MISS, edge; dur=1, origin; dur=51], tt_stable=[0], x-akamai-request-id=[107ed21c], x-cache=[TCP_MISS from a23-32-20-21.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-origin-response-time=[51,***********], x-tt-logid=[2025080809220701186FB993AC1340AF6C], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7e1f29a5042b4b937256b6a40d0261f5d649c23bab5ec18bbe6c4e11d44df6edf7bebc37111932648636429b0bba255120a554be60b23eb579a71b1c0cbf1f98f82bdd8e353445556aa44d4fdb91332ab1], x-tt-trace-id=[00-25080809220701186FB993AC1340AF6C-2F0040D55D006630-00], x-tt-trace-tag=[id=16;cdn-cache=hit;type=dyn]}
	at tiktokshop.open.sdk_java.invoke.ApiClient.handleResponse(ApiClient.java:1198)
	at tiktokshop.open.sdk_java.invoke.ApiClient.execute(ApiClient.java:1111)
	at tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api.affiliateCreator202410OrdersSearchPostWithHttpInfo(AffiliateCreatorV202410Api.java:214)
	at tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api.affiliateCreator202410OrdersSearchPost(AffiliateCreatorV202410Api.java:191)
	at com.genco.service.service.impl.TiktokOrderSyncServiceImpl.syncTiktokOrders(TiktokOrderSyncServiceImpl.java:81)
	at com.genco.admin.task.order.OrderTiktokPullTask.pullTiktokOrders(OrderTiktokPullTask.java:86)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:22:07.773",
                    "level": "ERROR",
                    "thread": "crmeb-scheduled-task-pool-3",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "TikTok订单拉取任务失败，taskId=6008, pageNo=1, err=TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"2025080809220701186FB993AC1340AF6C"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 01:22:07 GMT], expires=[Fri, 08 Aug 2025 01:22:07 GMT], pragma=[no-cache], server=[TLB], server-timing=[inner; dur=5, cdn-cache; desc=MISS, edge; dur=1, origin; dur=51], tt_stable=[0], x-akamai-request-id=[107ed21c], x-cache=[TCP_MISS from a23-32-20-21.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-origin-response-time=[51,***********], x-tt-logid=[2025080809220701186FB993AC1340AF6C], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7e1f29a5042b4b937256b6a40d0261f5d649c23bab5ec18bbe6c4e11d44df6edf7bebc37111932648636429b0bba255120a554be60b23eb579a71b1c0cbf1f98f82bdd8e353445556aa44d4fdb91332ab1], x-tt-trace-id=[00-25080809220701186FB993AC1340AF6C-2F0040D55D006630-00], x-tt-trace-tag=[id=16;cdn-cache=hit;type=dyn]}" }
                    
com.genco.common.exception.CrmebException: TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"2025080809220701186FB993AC1340AF6C"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 01:22:07 GMT], expires=[Fri, 08 Aug 2025 01:22:07 GMT], pragma=[no-cache], server=[TLB], server-timing=[inner; dur=5, cdn-cache; desc=MISS, edge; dur=1, origin; dur=51], tt_stable=[0], x-akamai-request-id=[107ed21c], x-cache=[TCP_MISS from a23-32-20-21.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-origin-response-time=[51,***********], x-tt-logid=[2025080809220701186FB993AC1340AF6C], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7e1f29a5042b4b937256b6a40d0261f5d649c23bab5ec18bbe6c4e11d44df6edf7bebc37111932648636429b0bba255120a554be60b23eb579a71b1c0cbf1f98f82bdd8e353445556aa44d4fdb91332ab1], x-tt-trace-id=[00-25080809220701186FB993AC1340AF6C-2F0040D55D006630-00], x-tt-trace-tag=[id=16;cdn-cache=hit;type=dyn]}
	at com.genco.service.service.impl.TiktokOrderSyncServiceImpl.syncTiktokOrders(TiktokOrderSyncServiceImpl.java:209)
	at com.genco.admin.task.order.OrderTiktokPullTask.pullTiktokOrders(OrderTiktokPullTask.java:86)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:23:20.332",
                    "level": "ERROR",
                    "thread": "crmeb-scheduled-task-pool-8",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"2025080809232001C72FBB3E875E413F37"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 01:23:20 GMT], expires=[Fri, 08 Aug 2025 01:23:20 GMT], pragma=[no-cache], server=[TLB], server-timing=[inner; dur=3, cdn-cache; desc=MISS, edge; dur=2, origin; dur=54], tt_stable=[0], x-akamai-request-id=[f39e098], x-cache=[TCP_MISS from a104-84-150-31.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-origin-response-time=[54,*************], x-tt-logid=[2025080809232001C72FBB3E875E413F37], x-tt-oec-error-level=[-], x-tt-trace-host=[0132609f1752e91dae0224cc3a1890964cdddbbd3f824a076e6b7753f4c1ddfdcc3add087f292183419cd490cbaa6aa98fa045cb05ea9357f7db4aa2f3cce5cb59bdc593c7d041cc31d9b9355db6be0d03828c00a8903bf373c763f01aeccc6724], x-tt-trace-id=[00-25080809232001C72FBB3E875E413F37-0832EEDC24426C14-00], x-tt-trace-tag=[id=16;cdn-cache=hit;type=dyn]}" }
                    
tiktokshop.open.sdk_java.invoke.ApiException: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"2025080809232001C72FBB3E875E413F37"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 01:23:20 GMT], expires=[Fri, 08 Aug 2025 01:23:20 GMT], pragma=[no-cache], server=[TLB], server-timing=[inner; dur=3, cdn-cache; desc=MISS, edge; dur=2, origin; dur=54], tt_stable=[0], x-akamai-request-id=[f39e098], x-cache=[TCP_MISS from a104-84-150-31.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-origin-response-time=[54,*************], x-tt-logid=[2025080809232001C72FBB3E875E413F37], x-tt-oec-error-level=[-], x-tt-trace-host=[0132609f1752e91dae0224cc3a1890964cdddbbd3f824a076e6b7753f4c1ddfdcc3add087f292183419cd490cbaa6aa98fa045cb05ea9357f7db4aa2f3cce5cb59bdc593c7d041cc31d9b9355db6be0d03828c00a8903bf373c763f01aeccc6724], x-tt-trace-id=[00-25080809232001C72FBB3E875E413F37-0832EEDC24426C14-00], x-tt-trace-tag=[id=16;cdn-cache=hit;type=dyn]}
	at tiktokshop.open.sdk_java.invoke.ApiClient.handleResponse(ApiClient.java:1198)
	at tiktokshop.open.sdk_java.invoke.ApiClient.execute(ApiClient.java:1111)
	at tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api.affiliateCreator202410OrdersSearchPostWithHttpInfo(AffiliateCreatorV202410Api.java:214)
	at tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api.affiliateCreator202410OrdersSearchPost(AffiliateCreatorV202410Api.java:191)
	at com.genco.service.service.impl.TiktokOrderSyncServiceImpl.syncTiktokOrders(TiktokOrderSyncServiceImpl.java:81)
	at com.genco.admin.task.order.OrderTiktokPullTask.pullTiktokOrders(OrderTiktokPullTask.java:86)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:23:20.333",
                    "level": "ERROR",
                    "thread": "crmeb-scheduled-task-pool-8",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "TikTok订单拉取任务失败，taskId=6009, pageNo=1, err=TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"2025080809232001C72FBB3E875E413F37"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 01:23:20 GMT], expires=[Fri, 08 Aug 2025 01:23:20 GMT], pragma=[no-cache], server=[TLB], server-timing=[inner; dur=3, cdn-cache; desc=MISS, edge; dur=2, origin; dur=54], tt_stable=[0], x-akamai-request-id=[f39e098], x-cache=[TCP_MISS from a104-84-150-31.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-origin-response-time=[54,*************], x-tt-logid=[2025080809232001C72FBB3E875E413F37], x-tt-oec-error-level=[-], x-tt-trace-host=[0132609f1752e91dae0224cc3a1890964cdddbbd3f824a076e6b7753f4c1ddfdcc3add087f292183419cd490cbaa6aa98fa045cb05ea9357f7db4aa2f3cce5cb59bdc593c7d041cc31d9b9355db6be0d03828c00a8903bf373c763f01aeccc6724], x-tt-trace-id=[00-25080809232001C72FBB3E875E413F37-0832EEDC24426C14-00], x-tt-trace-tag=[id=16;cdn-cache=hit;type=dyn]}" }
                    
com.genco.common.exception.CrmebException: TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"2025080809232001C72FBB3E875E413F37"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 01:23:20 GMT], expires=[Fri, 08 Aug 2025 01:23:20 GMT], pragma=[no-cache], server=[TLB], server-timing=[inner; dur=3, cdn-cache; desc=MISS, edge; dur=2, origin; dur=54], tt_stable=[0], x-akamai-request-id=[f39e098], x-cache=[TCP_MISS from a104-84-150-31.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-origin-response-time=[54,*************], x-tt-logid=[2025080809232001C72FBB3E875E413F37], x-tt-oec-error-level=[-], x-tt-trace-host=[0132609f1752e91dae0224cc3a1890964cdddbbd3f824a076e6b7753f4c1ddfdcc3add087f292183419cd490cbaa6aa98fa045cb05ea9357f7db4aa2f3cce5cb59bdc593c7d041cc31d9b9355db6be0d03828c00a8903bf373c763f01aeccc6724], x-tt-trace-id=[00-25080809232001C72FBB3E875E413F37-0832EEDC24426C14-00], x-tt-trace-tag=[id=16;cdn-cache=hit;type=dyn]}
	at com.genco.service.service.impl.TiktokOrderSyncServiceImpl.syncTiktokOrders(TiktokOrderSyncServiceImpl.java:209)
	at com.genco.admin.task.order.OrderTiktokPullTask.pullTiktokOrders(OrderTiktokPullTask.java:86)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:24:22.928",
                    "level": "ERROR",
                    "thread": "crmeb-scheduled-task-pool-10",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"20250808092423CAFA160631A94E406C12"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 01:24:23 GMT], expires=[Fri, 08 Aug 2025 01:24:23 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=38, origin; dur=14, inner; dur=4], tt_stable=[0], x-akamai-request-id=[cea43346.103e4e3a], x-cache=[TCP_MISS from a23-32-20-5.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-cache-remote=[TCP_MISS from a23-221-208-232.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[14,**************], x-parent-response-time=[51,**********], x-tt-logid=[20250808092423CAFA160631A94E406C12], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7e9fbb785d6920e3d864d07fcfe1c509920c5ecd531a4284cc3c19280ae6b7ff6e95af78ea435e44a83034fd655818d932c053aac6b39caf7c4381fecf73726f08cfce1fc0f472a0311061b5dbd0a1185f49bb58bd0c6d137c321189e93be75450], x-tt-trace-id=[00-250808092423CAFA160631A94E406C12-703745E30AABA364-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}" }
                    
tiktokshop.open.sdk_java.invoke.ApiException: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"20250808092423CAFA160631A94E406C12"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 01:24:23 GMT], expires=[Fri, 08 Aug 2025 01:24:23 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=38, origin; dur=14, inner; dur=4], tt_stable=[0], x-akamai-request-id=[cea43346.103e4e3a], x-cache=[TCP_MISS from a23-32-20-5.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-cache-remote=[TCP_MISS from a23-221-208-232.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[14,**************], x-parent-response-time=[51,**********], x-tt-logid=[20250808092423CAFA160631A94E406C12], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7e9fbb785d6920e3d864d07fcfe1c509920c5ecd531a4284cc3c19280ae6b7ff6e95af78ea435e44a83034fd655818d932c053aac6b39caf7c4381fecf73726f08cfce1fc0f472a0311061b5dbd0a1185f49bb58bd0c6d137c321189e93be75450], x-tt-trace-id=[00-250808092423CAFA160631A94E406C12-703745E30AABA364-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}
	at tiktokshop.open.sdk_java.invoke.ApiClient.handleResponse(ApiClient.java:1198)
	at tiktokshop.open.sdk_java.invoke.ApiClient.execute(ApiClient.java:1111)
	at tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api.affiliateCreator202410OrdersSearchPostWithHttpInfo(AffiliateCreatorV202410Api.java:214)
	at tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api.affiliateCreator202410OrdersSearchPost(AffiliateCreatorV202410Api.java:191)
	at com.genco.service.service.impl.TiktokOrderSyncServiceImpl.syncTiktokOrders(TiktokOrderSyncServiceImpl.java:81)
	at com.genco.admin.task.order.OrderTiktokPullTask.pullTiktokOrders(OrderTiktokPullTask.java:86)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:24:22.929",
                    "level": "ERROR",
                    "thread": "crmeb-scheduled-task-pool-10",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "TikTok订单拉取任务失败，taskId=6010, pageNo=1, err=TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"20250808092423CAFA160631A94E406C12"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 01:24:23 GMT], expires=[Fri, 08 Aug 2025 01:24:23 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=38, origin; dur=14, inner; dur=4], tt_stable=[0], x-akamai-request-id=[cea43346.103e4e3a], x-cache=[TCP_MISS from a23-32-20-5.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-cache-remote=[TCP_MISS from a23-221-208-232.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[14,**************], x-parent-response-time=[51,**********], x-tt-logid=[20250808092423CAFA160631A94E406C12], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7e9fbb785d6920e3d864d07fcfe1c509920c5ecd531a4284cc3c19280ae6b7ff6e95af78ea435e44a83034fd655818d932c053aac6b39caf7c4381fecf73726f08cfce1fc0f472a0311061b5dbd0a1185f49bb58bd0c6d137c321189e93be75450], x-tt-trace-id=[00-250808092423CAFA160631A94E406C12-703745E30AABA364-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}" }
                    
com.genco.common.exception.CrmebException: TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"20250808092423CAFA160631A94E406C12"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 01:24:23 GMT], expires=[Fri, 08 Aug 2025 01:24:23 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=38, origin; dur=14, inner; dur=4], tt_stable=[0], x-akamai-request-id=[cea43346.103e4e3a], x-cache=[TCP_MISS from a23-32-20-5.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-cache-remote=[TCP_MISS from a23-221-208-232.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[14,**************], x-parent-response-time=[51,**********], x-tt-logid=[20250808092423CAFA160631A94E406C12], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7e9fbb785d6920e3d864d07fcfe1c509920c5ecd531a4284cc3c19280ae6b7ff6e95af78ea435e44a83034fd655818d932c053aac6b39caf7c4381fecf73726f08cfce1fc0f472a0311061b5dbd0a1185f49bb58bd0c6d137c321189e93be75450], x-tt-trace-id=[00-250808092423CAFA160631A94E406C12-703745E30AABA364-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}
	at com.genco.service.service.impl.TiktokOrderSyncServiceImpl.syncTiktokOrders(TiktokOrderSyncServiceImpl.java:209)
	at com.genco.admin.task.order.OrderTiktokPullTask.pullTiktokOrders(OrderTiktokPullTask.java:86)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:26:14.071",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-24",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:28:25.043",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:30:41.930",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:06.106",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:26.771",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:43:31.672",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:29.702",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:37.334",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:38.897",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:35.081",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:54.468",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:51:12.967",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:01.294",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-24",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
